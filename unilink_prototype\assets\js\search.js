// Enhanced search functionality for posts, users, and organizations
function searchAll(searchTerm) {
    // First handle post search with existing function
    searchPosts(searchTerm);
    
    // If search term is empty, hide results dropdown
    if (searchTerm.trim() === '') {
        document.getElementById('searchResults').style.display = 'none';
        return;
    }
    
    // Fetch search results from server
    fetch('search_results.php?term=' + encodeURIComponent(searchTerm))
        .then(response => response.text())
        .then(text => {
            // Use the safe JSON parse function from main.js if available
            let data;
            try {
                // Check if the response contains PHP errors or warnings
                if (text.includes('<br>') || text.includes('<b>Warning') || text.includes('<b>Notice')) {
                    console.error('PHP error in response:', text);
                    // Try to extract JSON part if possible
                    const jsonStart = text.indexOf('{');
                    const jsonEnd = text.lastIndexOf('}');
                    if (jsonStart !== -1 && jsonEnd !== -1 && jsonEnd > jsonStart) {
                        const jsonPart = text.substring(jsonStart, jsonEnd + 1);
                        data = JSON.parse(jsonPart);
                    } else {
                        throw new Error('Invalid JSON response');
                    }
                } else {
                    data = JSON.parse(text);
                }
            } catch (error) {
                console.error('Error parsing search results:', error, 'Raw response:', text);
                return { users: [], organizations: [] };
            }
            
            const resultsContainer = document.getElementById('searchResults');
            
            // Clear previous results
            resultsContainer.innerHTML = '';
            
            // If we have results, show them
            if (data.users && data.users.length > 0 || data.organizations && data.organizations.length > 0) {
                // Add users section if there are user results
                if (data.users && data.users.length > 0) {
                    const userHeader = document.createElement('h6');
                    userHeader.className = 'dropdown-header';
                    userHeader.textContent = 'Users';
                    resultsContainer.appendChild(userHeader);
                    
                    data.users.forEach(user => {
                        const userItem = document.createElement('a');
                        userItem.className = 'dropdown-item d-flex align-items-center';
                        userItem.href = 'view_profile.php?id=' + user.id; // Changed to view_profile.php
                        
                        userItem.innerHTML = `
                            <img src="${user.profile_pic || 'img/profilepic.jpg'}" class="rounded-circle me-2" 
                                 width="32" height="32" style="object-fit: cover;">
                            <div>
                                <strong>${user.name}</strong>
                                ${user.campus ? `<small class="text-muted d-block">Campus: ${user.campus}</small>` : ''}
                            </div>
                        `;
                        
                        resultsContainer.appendChild(userItem);
                    });
                }
                
                // Add organizations section if there are organization results
                if (data.organizations && data.organizations.length > 0) {
                    const orgHeader = document.createElement('h6');
                    orgHeader.className = 'dropdown-header';
                    orgHeader.textContent = 'Organizations';
                    resultsContainer.appendChild(orgHeader);
                    
                    data.organizations.forEach(org => {
                        const orgItem = document.createElement('a');
                        orgItem.className = 'dropdown-item d-flex align-items-center';
                        orgItem.href = 'view_organization.php?id=' + org.id; // Changed to view_organization.php
                        
                        orgItem.innerHTML = `
                            <img src="${org.logo || 'img/org_default.jpg'}" class="rounded-circle me-2" 
                                 width="32" height="32" style="object-fit: cover;">
                            <div>
                                <strong>${org.name}</strong>
                                ${org.category ? `<small class="text-muted d-block">${org.category}</small>` : ''}
                            </div>
                        `;
                        
                        resultsContainer.appendChild(orgItem);
                    });
                }
                
                // Show the results dropdown
                resultsContainer.style.display = 'block';
            } else {
                // No results found
                const noResults = document.createElement('div');
                noResults.className = 'dropdown-item text-center text-muted';
                noResults.textContent = 'No users or organizations found';
                resultsContainer.appendChild(noResults);
                resultsContainer.style.display = 'block';
            }
        })
        .catch(error => {
            console.error('Search error:', error);
            // Show error in results dropdown
            const resultsContainer = document.getElementById('searchResults');
            resultsContainer.innerHTML = `
                <div class="dropdown-item text-center text-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    Error loading search results
                </div>
            `;
            resultsContainer.style.display = 'block';
        });
}

// Close search results when clicking outside
document.addEventListener('click', function(event) {
    const searchResults = document.getElementById('searchResults');
    const searchInput = document.querySelector('input[type="search"]');
    
    if (searchResults && !searchResults.contains(event.target) && event.target !== searchInput) {
        searchResults.style.display = 'none';
    }
});

// Add CSS for search results dropdown
document.addEventListener('DOMContentLoaded', function() {
    const style = document.createElement('style');
    style.textContent = `
        .search-results-dropdown {
            max-height: 350px;
            overflow-y: auto;
            z-index: 1050;
            position: absolute;
            top: 100%; /* Position below the search bar */
            left: 0;
            width: 500px;
            margin-top: 2px; /* Small gap between search bar and dropdown */
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            border-radius: 0.25rem;
        }
        .search-results-dropdown .dropdown-item {
            white-space: normal;
            padding: 0.5rem 1rem;
        }
        .search-results-dropdown .dropdown-item:hover {
            background-color: #f8f9fa;
        }
        /* Dark mode support */
        [data-bs-theme="dark"] .search-results-dropdown {
            background-color: #343a40;
            border-color: #495057;
        }
        [data-bs-theme="dark"] .search-results-dropdown .dropdown-item:hover {
            background-color: #495057;
        }
    `;
    document.head.appendChild(style);
});




