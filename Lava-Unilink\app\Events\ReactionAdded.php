<?php

namespace App\Events;

use App\Models\Reaction;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class ReactionAdded implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $reaction;
    public $reactionCounts;

    /**
     * Create a new event instance.
     */
    public function __construct(Reaction $reaction, array $reactionCounts)
    {
        $this->reaction = $reaction;
        $this->reactionCounts = $reactionCounts;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        $reactableType = strtolower(class_basename($this->reaction->reactable_type));

        return [
            new Channel($reactableType . '.' . $this->reaction->reactable_id . '.reactions'),
        ];
    }

    /**
     * Get the data to broadcast.
     *
     * @return array
     */
    public function broadcastWith(): array
    {
        return [
            'reaction' => [
                'id' => $this->reaction->id,
                'type' => $this->reaction->type,
                'reactable_type' => strtolower(class_basename($this->reaction->reactable_type)),
                'reactable_id' => $this->reaction->reactable_id,
                'user' => [
                    'id' => $this->reaction->user->id,
                    'name' => $this->reaction->user->name,
                    'avatar' => $this->reaction->user->avatar,
                ],
                'created_at' => $this->reaction->created_at->toISOString(),
            ],
            'reaction_counts' => $this->reactionCounts,
        ];
    }

    /**
     * The event's broadcast name.
     *
     * @return string
     */
    public function broadcastAs(): string
    {
        return 'reaction.added';
    }
}
