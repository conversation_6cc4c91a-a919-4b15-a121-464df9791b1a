<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Post;
use App\Models\Organization;
use App\Events\PostCreated;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;

class PostController extends Controller
{
    /**
     * Display a listing of posts.
     */
    public function index(Request $request): JsonResponse
    {
        $query = Post::with(['user', 'organization', 'reactions'])
                    ->withCount(['comments', 'reactions']);

        // Filter by organization
        if ($request->has('organization_id')) {
            $query->where('organization_id', $request->organization_id);
        }

        // Filter by user
        if ($request->has('user_id')) {
            $query->where('user_id', $request->user_id);
        }

        // Filter by type
        if ($request->has('type')) {
            $query->where('type', $request->type);
        }

        // Filter by visibility
        if ($request->has('visibility')) {
            $query->where('visibility', $request->visibility);
        }

        // Only show published posts unless user is admin or post owner
        if (!Auth::user()->isAdmin()) {
            $query->published();
        }

        // Search in title and content
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('content', 'like', "%{$search}%");
            });
        }

        // Order by pinned first, then by creation date
        $query->orderBy('is_pinned', 'desc')
              ->orderBy('created_at', 'desc');

        $posts = $query->paginate($request->get('per_page', 15));

        return response()->json($posts);
    }

    /**
     * Store a newly created post.
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'type' => 'required|string|in:announcement,discussion,event,news',
            'media' => 'nullable|array',
            'media.*' => 'file|mimes:jpeg,png,jpg,gif,pdf,doc,docx|max:10240',
            'visibility' => 'required|string|in:public,members_only,private',
            'organization_id' => 'nullable|exists:organizations,id',
            'is_pinned' => 'boolean',
            'comments_enabled' => 'boolean',
            'published_at' => 'nullable|date',
        ]);

        // Check if user can post to organization
        if ($validated['organization_id']) {
            $organization = Organization::find($validated['organization_id']);
            if (!$organization->hasMember(Auth::user()) && !Auth::user()->isAdmin()) {
                return response()->json(['message' => 'You are not a member of this organization'], 403);
            }
        }

        // Handle media uploads
        if ($request->hasFile('media')) {
            $mediaFiles = [];
            foreach ($request->file('media') as $file) {
                $path = $file->store('posts/media', 'public');
                $mediaFiles[] = [
                    'path' => $path,
                    'name' => $file->getClientOriginalName(),
                    'type' => $file->getMimeType(),
                    'size' => $file->getSize(),
                ];
            }
            $validated['media'] = $mediaFiles;
        }

        $validated['user_id'] = Auth::id();

        // Set published_at to now if not provided and not a draft
        if (!isset($validated['published_at'])) {
            $validated['published_at'] = now();
        }

        $post = Post::create($validated);
        $post->load(['user', 'organization', 'reactions']);

        // Fire the PostCreated event for real-time updates
        event(new PostCreated($post));

        return response()->json([
            'message' => 'Post created successfully',
            'post' => $post
        ], 201);
    }

    /**
     * Display the specified post.
     */
    public function show(Post $post): JsonResponse
    {
        // Check visibility permissions
        if ($post->visibility === 'private' && $post->user_id !== Auth::id() && !Auth::user()->isAdmin()) {
            return response()->json(['message' => 'Post not found'], 404);
        }

        if ($post->visibility === 'members_only' && $post->organization_id) {
            $organization = $post->organization;
            if (!$organization->hasMember(Auth::user()) && !Auth::user()->isAdmin()) {
                return response()->json(['message' => 'Post not found'], 404);
            }
        }

        $post->load([
            'user',
            'organization',
            'comments' => function ($query) {
                $query->topLevel()->with(['user', 'replies.user', 'reactions'])->latest();
            },
            'reactions.user'
        ]);

        $post->loadCount(['comments', 'reactions']);

        return response()->json($post);
    }

    /**
     * Update the specified post.
     */
    public function update(Request $request, Post $post): JsonResponse
    {
        // Check if user can update this post
        if ($post->user_id !== Auth::id() && !Auth::user()->isAdmin()) {
            // Check if user is admin of the organization
            if ($post->organization_id) {
                $organization = $post->organization;
                if (!$organization->hasAdmin(Auth::user())) {
                    return response()->json(['message' => 'Unauthorized'], 403);
                }
            } else {
                return response()->json(['message' => 'Unauthorized'], 403);
            }
        }

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'type' => 'required|string|in:announcement,discussion,event,news',
            'media' => 'nullable|array',
            'media.*' => 'file|mimes:jpeg,png,jpg,gif,pdf,doc,docx|max:10240',
            'visibility' => 'required|string|in:public,members_only,private',
            'is_pinned' => 'boolean',
            'comments_enabled' => 'boolean',
            'published_at' => 'nullable|date',
        ]);

        // Handle media uploads
        if ($request->hasFile('media')) {
            // Delete old media files
            if ($post->media) {
                foreach ($post->media as $media) {
                    Storage::disk('public')->delete($media['path']);
                }
            }

            $mediaFiles = [];
            foreach ($request->file('media') as $file) {
                $path = $file->store('posts/media', 'public');
                $mediaFiles[] = [
                    'path' => $path,
                    'name' => $file->getClientOriginalName(),
                    'type' => $file->getMimeType(),
                    'size' => $file->getSize(),
                ];
            }
            $validated['media'] = $mediaFiles;
        }

        $post->update($validated);

        return response()->json([
            'message' => 'Post updated successfully',
            'post' => $post->load(['user', 'organization', 'reactions'])
        ]);
    }

    /**
     * Remove the specified post.
     */
    public function destroy(Post $post): JsonResponse
    {
        // Check if user can delete this post
        if ($post->user_id !== Auth::id() && !Auth::user()->isAdmin()) {
            // Check if user is admin of the organization
            if ($post->organization_id) {
                $organization = $post->organization;
                if (!$organization->hasAdmin(Auth::user())) {
                    return response()->json(['message' => 'Unauthorized'], 403);
                }
            } else {
                return response()->json(['message' => 'Unauthorized'], 403);
            }
        }

        // Delete associated media files
        if ($post->media) {
            foreach ($post->media as $media) {
                Storage::disk('public')->delete($media['path']);
            }
        }

        $post->delete();

        return response()->json(['message' => 'Post deleted successfully']);
    }
}
