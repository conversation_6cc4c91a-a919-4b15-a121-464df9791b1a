// Notification popup functionality
document.addEventListener('DOMContentLoaded', function() {
    const notificationIcon = document.getElementById('notification-icon');
    const notificationPopup = document.getElementById('notification-popup');
    const closePopup = document.getElementById('close-popup');
    
    if (notificationIcon && notificationPopup && closePopup) {
        // Initially hide the popup
        notificationPopup.style.display = 'none';
        
        notificationIcon.addEventListener('click', function(event) {
            event.preventDefault(); // Prevent default link behavior
            console.log('Notification icon clicked');
            
            // Toggle popup visibility
            if (notificationPopup.style.display === 'none') {
                // Show popup
                notificationPopup.style.display = 'block';
                setTimeout(function() {
                    notificationPopup.classList.add('show');
                }, 10);
            } else {
                // Hide popup
                notificationPopup.classList.remove('show');
                setTimeout(function() {
                    notificationPopup.style.display = 'none';
                }, 300);
            }
        });
        
        closePopup.addEventListener('click', function() {
            console.log('Close button clicked');
            // Hide popup
            notificationPopup.classList.remove('show');
            setTimeout(function() {
                notificationPopup.style.display = 'none';
            }, 300);
        });
        
        // Close popup when clicking outside
        document.addEventListener('click', function(event) {
            if (notificationPopup.style.display === 'block' && 
                !notificationPopup.contains(event.target) && 
                event.target !== notificationIcon &&
                !notificationIcon.contains(event.target)) {
                notificationPopup.classList.remove('show');
                setTimeout(function() {
                    notificationPopup.style.display = 'none';
                }, 300);
            }
        });
        
        // Mark notifications as read when clicked
        const notificationItems = document.querySelectorAll('.notification-item');
        notificationItems.forEach(item => {
            item.addEventListener('click', function() {
                const listItem = this.closest('.list-group-item');
                if (listItem.classList.contains('notification-unread')) {
                    listItem.classList.remove('notification-unread');
                    
                    // Here you would typically make an AJAX call to mark as read in the database
                    // For example:
                    // const notificationId = listItem.dataset.notificationId;
                    // fetch('mark_notification_read.php?id=' + notificationId);
                }
            });
        });
    } else {
        console.error('Notification elements not found in the DOM');
    }
});

