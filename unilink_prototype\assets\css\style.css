/* Color Variables */
:root {
    --color-lightest: #EEEEEE; /* Light Gray */
    --color-third-darkest: #7BC74D; /* Green */
    --color-second-darkest: #393E46; /* Dark Gray */
    --color-darkest: #222831; /* Dark Navy */
    
    /* Complete Bootstrap overrides */
    --bs-primary: #7BC74D;
    --bs-primary-rgb: 123, 199, 77;
    --bs-secondary: #393E46;
    --bs-secondary-rgb: 57, 62, 70;
    --bs-success: #7BC74D;
    --bs-success-rgb: 123, 199, 77;
    --bs-info: #EEEEEE;
    --bs-info-rgb: 238, 238, 238;
    --bs-warning: #7BC74D;
    --bs-warning-rgb: 123, 199, 77;
    --bs-danger: #222831;
    --bs-danger-rgb: 34, 40, 49;
    --bs-light: #EEEEEE;
    --bs-light-rgb: 238, 238, 238;
    --bs-dark: #222831;
    --bs-dark-rgb: 34, 40, 49;
    
    /* <PERSON>trap subtle colors */
    --bs-primary-bg-subtle: rgba(123, 199, 77, 0.15);
    --bs-secondary-bg-subtle: rgba(57, 62, 70, 0.15);
    --bs-success-bg-subtle: rgba(123, 199, 77, 0.15);
    --bs-info-bg-subtle: rgba(238, 238, 238, 0.15);
    --bs-warning-bg-subtle: rgba(123, 199, 77, 0.15);
    --bs-danger-bg-subtle: rgba(34, 40, 49, 0.15);
    
    /* Text emphasis colors */
    --bs-primary-text-emphasis: #6AB33C;
    --bs-secondary-text-emphasis: #2d3238;
    --bs-success-text-emphasis: #6AB33C;
    --bs-info-text-emphasis: #d6d6d6;
    --bs-warning-text-emphasis: #6AB33C;
    --bs-danger-text-emphasis: #191d22;
    
    /* Border subtle colors */
    --bs-primary-border-subtle: rgba(123, 199, 77, 0.3);
    --bs-secondary-border-subtle: rgba(57, 62, 70, 0.3);
    --bs-success-border-subtle: rgba(123, 199, 77, 0.3);
    --bs-info-border-subtle: rgba(238, 238, 238, 0.3);
    --bs-warning-border-subtle: rgba(123, 199, 77, 0.3);
    --bs-danger-border-subtle: rgba(34, 40, 49, 0.3);
}

/* Additional Bootstrap component overrides */
.btn-success, .btn-warning {
    background-color: var(--color-third-darkest);
    border-color: var(--color-third-darkest);
}

.btn-success:hover, .btn-warning:hover {
    background-color: #6AB33C;
    border-color: #6AB33C;
}

.alert-success, .alert-warning {
    background-color: var(--bs-primary-bg-subtle);
    border-color: var(--bs-primary-border-subtle);
    color: var(--bs-primary-text-emphasis);
}

.alert-danger {
    background-color: var(--bs-danger-bg-subtle);
    border-color: var(--bs-danger-border-subtle);
    color: var(--bs-danger-text-emphasis);
}

.bg-success, .bg-warning {
    background-color: var(--color-third-darkest) !important;
}

.text-success, .text-warning {
    color: var(--color-third-darkest) !important;
}

/* Form controls */
.form-check-input:checked {
    background-color: var(--color-third-darkest);
    border-color: var(--color-third-darkest);
}

.form-control:focus, .form-select:focus {
    border-color: var(--color-third-darkest);
    box-shadow: 0 0 0 0.25rem rgba(123, 199, 77, 0.25);
}

/* Progress bars */
.progress-bar {
    background-color: var(--color-third-darkest);
}

/* List groups */
.list-group-item.active {
    background-color: var(--color-third-darkest);
    border-color: var(--color-third-darkest);
}

/* General styles */
body {
    background-color: var(--color-lightest);
    min-height: 100vh;
    display: flex;
    flex-direction: column;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Button styling */
.btn-primary {
    background-color: var(--color-third-darkest);
    border-color: var(--color-third-darkest);
}

.btn-primary:hover {
    background-color: #6AB33C; /* Slightly darker green */
    border-color: #6AB33C;
}

.btn-outline-primary {
    color: var(--color-third-darkest);
    border-color: var(--color-third-darkest);
    transition: all 0.3s ease;
}

.btn-outline-primary:hover {
    background-color: rgba(123, 199, 77, 0.1);
    color: var(--color-third-darkest);
    border-color: var(--color-third-darkest);
}

.btn-outline-primary.active {
    background-color: var(--color-third-darkest);
    color: var(--color-lightest);
    border-color: var(--color-third-darkest);
}

.btn-outline-primary.active:hover {
    background-color: var(--color-third-darkest);
    color: var(--color-lightest);
}

/* Text and background colors */
.bg-primary {
    background-color: var(--color-third-darkest) !important;
}

.text-primary {
    color: var(--color-third-darkest) !important;
}

/* Navbar styling */
.navbar {
    background-color: var(--color-darkest) !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    height: 65px; /* Set a specific height for the navbar */
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
}

.navbar-dark {
    background-color: var(--color-darkest) !important;
}

.navbar-brand {
    font-size: 1.5rem;
    padding-top: 0;
    padding-bottom: 0;
    height: 40px;
    display: flex;
    align-items: center;
}

.navbar-brand .brand-uni {
    color: var(--color-third-darkest);
}

.navbar-brand .brand-link {
    color: var(--color-lightest);
}

.navbar-nav .nav-link {
    padding-top: 0.50rem;
    padding-bottom: 0.50rem;
    height: 100%;
    display: flex;
    align-items: center;
}

/* Card styling */
.card-header.bg-primary {
    background-color: var(--color-third-darkest) !important;
}

/* Notification badge */
.notification-badge {
    position: absolute;
    top: 0;
    right: 0;
    background-color: #dc3545 !important; /* Bootstrap red */
    color: white;
    border-radius: 50%;
    padding: 0.1rem 0.5rem;
    font-size: 0.75rem;
    transform: translate(40%, -30%);
}

.notification-nav-link {
    position: relative;
}

/* Header */
header {
    background-color: var(--color-darkest);
    color: var(--color-lightest);
}

/* Post styling */
.post {
    background-color: white;
    border: 1px solid rgba(0, 0, 0, 0.1);
}

/* Scrollbar */
::-webkit-scrollbar-thumb {
    background-color: var(--color-third-darkest);
}

::-webkit-scrollbar-thumb:hover {
    background-color: var(--color-second-darkest);
}

/* Notification popup */
.notification-popup {
    position: fixed;
    top: 70px; /* Match navbar height */
    right: -300px;
    width: 300px;
    background-color: white;
    color: var(--color-darkest);
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    z-index: 1000;
    transition: right 0.3s ease;
    overflow: hidden;
    max-height: calc(100vh - 70px); /* Adjust based on navbar height */
    display: none;
}

.notification-popup.show {
    right: 20px;
}

.popup-content {
    padding: 0;
}

.popup-header {
    background-color: var(--color-third-darkest);
    color: white;
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.popup-header h5 {
    margin: 0;
    font-weight: 600;
    font-size: 1.1rem;
}

.popup-header .btn-close {
    background-color: transparent;
    color: white;
    opacity: 0.8;
    padding: 0;
    font-size: 1.2rem;
    filter: invert(1) brightness(200%);
}

.popup-header .btn-close:hover {
    opacity: 1;
}

.popup-body {
    max-height: calc(80vh - 60px);
    overflow-y: auto;
}

.popup-content ul.list-group {
    border-radius: 0;
    max-height: 400px;
    overflow-y: auto;
}

.popup-footer {
    padding: 10px 15px;
    text-align: center;
    border-top: 1px solid rgba(0, 0, 0, 0.1);
}

.popup-footer a {
    color: var(--color-third-darkest);
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
}

.popup-footer a:hover {
    text-decoration: underline;
}

/* Notification items */
.list-group-item {
    padding: 12px 15px;
    border-left: none;
    border-right: none;
    transition: background-color 0.2s ease;
}

.list-group-item:first-child {
    border-top: none;
}

.list-group-item:hover {
    background-color: rgba(0, 0, 0, 0.03);
}

.notification-unread {
    background-color: rgba(123, 199, 77, 0.1);
    position: relative;
}

.notification-unread::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 4px;
    background-color: var(--color-third-darkest);
}

.notification-item {
    display: flex;
    align-items: flex-start;
    cursor: pointer;
}

.notification-icon {
    margin-right: 12px;
    color: var(--color-third-darkest);
    font-size: 1.2rem;
    padding-top: 3px;
}

.notification-content {
    flex: 1;
}

.notification-title {
    font-weight: 600;
    margin-bottom: 3px;
    font-size: 0.95rem;
}

.notification-text {
    margin: 0 0 5px 0;
    font-size: 0.85rem;
    color: #666;
}

.notification-time {
    font-size: 0.75rem;
    color: #888;
    display: block;
}

/* Dark mode adjustments for notification popup */
[data-bs-theme="dark"] .notification-popup {
    background-color: #393E46;
    color: #EEEEEE;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.4);
}

[data-bs-theme="dark"] .popup-header {
    background-color: var(--color-third-darkest);
    border-bottom: 1px solid rgba(238, 238, 238, 0.1);
}

[data-bs-theme="dark"] .list-group-item {
    background-color: #393E46;
    border-color: rgba(238, 238, 238, 0.1);
    color: #EEEEEE;
}

[data-bs-theme="dark"] .list-group-item:hover {
    background-color: #2c3037;
}

[data-bs-theme="dark"] .notification-unread {
    background-color: rgba(123, 199, 77, 0.15);
}

[data-bs-theme="dark"] .notification-text {
    color: #CCCCCC;
}

[data-bs-theme="dark"] .notification-time {
    color: #AAAAAA;
}

[data-bs-theme="dark"] .popup-footer {
    border-top: 1px solid rgba(238, 238, 238, 0.1);
}

[data-bs-theme="dark"] .popup-footer a {
    color: var(--color-third-darkest);
}

/* Feed content adjustments */
.feed-content {
    max-width: 540px;
    margin: 0 auto;
    max-height: calc(100vh - 150px); /* Adjust height based on header/footer */
    overflow-y: auto;
    overflow-x: hidden;
    /* padding-right: 10px; Add some padding for the scrollbar */
    scrollbar-width: thin; /* For Firefox */
}

/* Card adjustments to match feed width */
.feed-content .card {
    width: 100%;
}

/* Facebook embed styling */
.fb-embed {
    width: 100%;
    max-width: 100%;
    overflow: hidden;
}

.fb-embed iframe {
    width: 100%;
    max-width: 100%;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .feed-content {
        max-width: 100%;
    }
}

/* Make the main container fixed height */
.main-container {
    height: 100vh;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

/* Make the content area take remaining height */
.content-area {
    flex: 1;
    overflow: hidden;
    position: relative;
    padding-bottom: 20px; /* Add some bottom padding since footer is removed */
}

/* Sidebar scrolling */
.sidebar-content {
    max-height: calc(100vh - 100px); /* Adjust height since footer is removed */
    overflow-y: auto;
    overflow-x: hidden;
}

/* Search form alignment */
@media (min-width: 992px) {
    .search-form {
        width: 25%;
        margin-left: 0;
    }
}

@media (max-width: 991.98px) {
    .search-form {
        width: 100%;
        margin: 0.5rem 0;
    }
}

/* Page header styling */
.page-header {
    background-color: var(--bs-primary);
    color: white;
    padding: 2rem 0;
    margin-bottom: 2rem;
    position: relative;
}

.page-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 10px;
    background: linear-gradient(90deg, var(--bs-primary), var(--bs-secondary));
}

.page-header h2 {
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.page-header p {
    font-size: 1.1rem;
    opacity: 0.9;
    max-width: 700px;
    margin: 0 auto;
}

/* Card hover effects */
.campus-card, .scholarship-card, .org-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.campus-card:hover, .scholarship-card:hover, .org-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1) !important;
}

/* Image styling for cards */
.campus-card img {
    height: 200px;
    object-fit: cover;
}

.scholarship-img {
    height: 160px;
    object-fit: cover;
}

.org-logo {
    height: 180px;
    object-fit: cover;
    object-position: center;
}

/* Organization card styling */
.org-title {
    font-size: 1rem;
    font-weight: 600;
    text-align: center;
    margin-bottom: 0.5rem;
}

/* Admin action buttons */
.btn-group .btn {
    padding: 0.25rem 0.5rem;
}

/* Modal customizations */
.modal-header.bg-primary {
    color: white;
}

.btn-close-white {
    filter: invert(1) grayscale(100%) brightness(200%);
}

/* Login and Register styles */
.login-container, .register-container {
    max-width: 500px;
    width: 100%;
    padding: 20px;
}

.login-card, .register-card {
    border-radius: 1rem;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.login-header, .register-header {
    background-color: var(--bs-primary);
    color: white;
    border-radius: 1rem 1rem 0 0;
    padding: 20px;
    text-align: center;
}

.login-body, .register-body {
    padding: 30px;
}

.login-footer, .register-footer {
    text-align: center;
    margin-top: 20px;
}

.btn-login, .btn-register {
    width: 100%;
    padding: 12px;
    font-weight: 500;
}

/* Login specific styles */
body.login-page, body.register-page {
    background-color: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    padding: 20px 0;
}

.form-floating {
    margin-bottom: 20px;
}

/* Dark mode variables - enhanced version */
[data-bs-theme="dark"] {
    --color-lightest: #222831;
    --color-third-darkest: #7BC74D;
    --color-second-darkest: #EEEEEE;
    --color-darkest: #EEEEEE;
    
    /* Bootstrap dark mode overrides */
    --bs-body-color: #EEEEEE;
    --bs-body-bg: #222831;
    
    /* Card and component backgrounds */
    --bs-card-bg: #393E46;
    --bs-dropdown-bg: #393E46;
    --bs-dropdown-link-hover-bg: #2c3037;
    --bs-dropdown-link-active-bg: #7BC74D;
    
    /* Border colors */
    --bs-border-color: rgba(238, 238, 238, 0.2);
    
    /* Form controls */
    --bs-form-control-bg: #2c3037;
    --bs-form-control-color: #EEEEEE;
    
    /* Buttons */
    --bs-btn-color: #EEEEEE;
    
    /* List group */
    --bs-list-group-bg: #393E46;
    --bs-list-group-border-color: rgba(238, 238, 238, 0.1);
    --bs-list-group-action-hover-bg: #2c3037;
    --bs-list-group-action-active-bg: #2c3037;
    
    /* Organization variables */
    --org-bg-color: #393E46;
    --org-text-color: #EEEEEE;
    --org-border-color: rgba(238, 238, 238, 0.1);
    --org-hover-color: #2c3037;
    --org-accent-color: #7BC74D;
}

/* Organization styles - light mode defaults */
.org-header {
    background-color: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.org-logo {
    width: 100px;
    height: 100px;
    object-fit: cover;
    border-radius: 50%;
    border: 3px solid #0d6efd;
}

.stat-card {
    text-align: center;
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 15px;
    background-color: #fff;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.stat-card i {
    font-size: 2rem;
    margin-bottom: 10px;
    color: #0d6efd;
}

.stat-card h3 {
    font-size: 1.5rem;
    margin-bottom: 5px;
}

.member-card {
    display: flex;
    align-items: center;
    padding: 10px;
    border-bottom: 1px solid #eee;
}

.member-card:last-child {
    border-bottom: none;
}

.member-pic {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 10px;
    object-fit: cover;
}

.post-card {
    border: 1px solid #eee;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 15px;
    background-color: #fff;
}

.event-card {
    border: 1px solid #eee;
    border-radius: 10px;
    overflow: hidden;
    background-color: #fff;
}

.event-date {
    background-color: #0d6efd;
    color: white;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

/* Organization buttons */
.btn-org-primary {
    background-color: #0d6efd;
    border-color: #0d6efd;
    color: white;
}

.btn-org-primary:hover {
    background-color: #0b5ed7;
    border-color: #0a58ca;
    color: white;
}

.btn-org-outline {
    background-color: transparent;
    border-color: #0d6efd;
    color: #0d6efd;
}

.btn-org-outline:hover {
    background-color: #0d6efd;
    color: white;
}

/* Dark mode overrides for organization components */
[data-bs-theme="dark"] .org-header {
    background-color: var(--org-bg-color);
    border-color: var(--org-border-color);
}

[data-bs-theme="dark"] .stat-card {
    background-color: var(--org-bg-color);
    border-color: var(--org-border-color);
    color: var(--org-text-color);
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.2);
}

[data-bs-theme="dark"] .stat-card i {
    color: var(--org-accent-color);
}

[data-bs-theme="dark"] .member-card {
    border-color: var(--org-border-color);
}

[data-bs-theme="dark"] .post-card {
    background-color: var(--org-bg-color);
    border-color: var(--org-border-color);
    color: var(--org-text-color);
}

[data-bs-theme="dark"] .event-card {
    background-color: var(--org-bg-color);
    border-color: var(--org-border-color);
    color: var(--org-text-color);
}

[data-bs-theme="dark"] .event-date {
    background-color: var(--org-accent-color);
    color: #222831;
}

[data-bs-theme="dark"] .btn-org-primary {
    background-color: var(--org-accent-color);
    border-color: var(--org-accent-color);
    color: #222831;
}

[data-bs-theme="dark"] .btn-org-primary:hover {
    background-color: #6ab33e;
    border-color: #6ab33e;
    color: #222831;
}

[data-bs-theme="dark"] .btn-org-outline {
    background-color: transparent;
    border-color: var(--org-accent-color);
    color: var(--org-accent-color);
}

[data-bs-theme="dark"] .btn-org-outline:hover {
    background-color: var(--org-accent-color);
    color: #222831;
}

[data-bs-theme="dark"] .card {
    background-color: var(--org-bg-color);
    border-color: var(--org-border-color);
}

[data-bs-theme="dark"] .card-header {
    background-color: rgba(0, 0, 0, 0.2);
    border-color: var(--org-border-color);
}

[data-bs-theme="dark"] .card-footer {
    background-color: rgba(0, 0, 0, 0.2);
    border-color: var(--org-border-color);
}

[data-bs-theme="dark"] .list-group-item {
    background-color: var(--org-bg-color);
    border-color: var(--org-border-color);
    color: var(--org-text-color);
}

[data-bs-theme="dark"] .list-group-item-action:hover {
    background-color: var(--org-hover-color);
}

[data-bs-theme="dark"] .text-muted {
    color: rgba(238, 238, 238, 0.6) !important;
}

[data-bs-theme="dark"] .form-control,
[data-bs-theme="dark"] .form-select {
    background-color: var(--bs-form-control-bg);
    border-color: var(--org-border-color);
    color: var(--bs-form-control-color);
}

[data-bs-theme="dark"] .form-control:focus,
[data-bs-theme="dark"] .form-select:focus {
    border-color: var(--org-accent-color);
    box-shadow: 0 0 0 0.25rem rgba(123, 199, 77, 0.25);
}

[data-bs-theme="dark"] .nav-tabs {
    border-color: var(--org-border-color);
}

[data-bs-theme="dark"] .nav-tabs .nav-link {
    color: var(--org-text-color);
}

[data-bs-theme="dark"] .nav-tabs .nav-link:hover {
    border-color: var(--org-border-color);
}

[data-bs-theme="dark"] .nav-tabs .nav-link.active {
    background-color: var(--org-bg-color);
    border-color: var(--org-border-color);
    border-bottom-color: var(--org-bg-color);
    color: var(--org-accent-color);
}

[data-bs-theme="dark"] .pagination .page-link {
    background-color: var(--org-bg-color);
    border-color: var(--org-border-color);
    color: var(--org-text-color);
}

[data-bs-theme="dark"] .pagination .page-item.active .page-link {
    background-color: var(--org-accent-color);
    border-color: var(--org-accent-color);
    color: #222831;
}

[data-bs-theme="dark"] .pagination .page-link:hover {
    background-color: var(--org-hover-color);
    border-color: var(--org-border-color);
}

/* Dark mode theme switcher styles */
.dropdown-menu-dark .dropdown-item.active, 
.dropdown-menu-dark .dropdown-item:active {
    background-color: var(--org-accent-color);
    color: #222831;
}

/* Organization-related dark mode styles */
[data-bs-theme="dark"] {
    /* Organization general styles */
    --org-bg-color: #393E46;
    --org-text-color: #EEEEEE;
    --org-border-color: rgba(238, 238, 238, 0.1);
    --org-hover-color: #2c3037;
    --org-accent-color: #7BC74D;
}

/* Organization cards in listings */
[data-bs-theme="dark"] .organization-card {
    background-color: var(--org-bg-color);
    border-color: var(--org-border-color);
    color: var(--org-text-color);
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.2);
}

[data-bs-theme="dark"] .organization-card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.3);
    border-color: var(--org-accent-color);
}

[data-bs-theme="dark"] .organization-card .card-title {
    color: var(--org-text-color);
}

[data-bs-theme="dark"] .organization-card .badge {
    background-color: var(--org-accent-color);
    color: #222831;
}

/* Organization dashboard */
[data-bs-theme="dark"] .org-header {
    background-color: var(--org-bg-color);
    border-color: var(--org-border-color);
}

[data-bs-theme="dark"] .org-logo {
    border: 3px solid var(--org-accent-color);
}

[data-bs-theme="dark"] .stat-card {
    background-color: var(--org-bg-color);
    border-color: var(--org-border-color);
    color: var(--org-text-color);
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.2);
}

[data-bs-theme="dark"] .stat-card i {
    color: var(--org-accent-color);
}

/* Organization members */
[data-bs-theme="dark"] .member-card {
    background-color: var(--org-bg-color);
    border-bottom-color: var(--org-border-color);
}

[data-bs-theme="dark"] .member-card:hover {
    background-color: var(--org-hover-color);
}

[data-bs-theme="dark"] .member-pic {
    border: 2px solid var(--org-accent-color);
}

/* Organization posts */
[data-bs-theme="dark"] .post-card {
    background-color: var(--org-bg-color);
    border-color: var(--org-border-color);
    color: var(--org-text-color);
}

[data-bs-theme="dark"] .post-card .post-header {
    border-bottom-color: var(--org-border-color);
}

[data-bs-theme="dark"] .post-card .post-footer {
    border-top-color: var(--org-border-color);
}

/* Organization events */
[data-bs-theme="dark"] .event-card {
    background-color: var(--org-bg-color);
    border-color: var(--org-border-color);
    color: var(--org-text-color);
}

[data-bs-theme="dark"] .event-card .event-date {
    background-color: var(--org-accent-color);
    color: #222831;
}

[data-bs-theme="dark"] .event-card:hover {
    background-color: var(--org-hover-color);
    border-color: var(--org-accent-color);
}

/* Organization forms */
[data-bs-theme="dark"] .org-form {
    background-color: var(--org-bg-color);
    border-color: var(--org-border-color);
    color: var(--org-text-color);
}

[data-bs-theme="dark"] .org-form .form-control,
[data-bs-theme="dark"] .org-form .form-select {
    background-color: #2c3037;
    border-color: var(--org-border-color);
    color: var(--org-text-color);
}

[data-bs-theme="dark"] .org-form .form-control:focus,
[data-bs-theme="dark"] .org-form .form-select:focus {
    border-color: var(--org-accent-color);
    box-shadow: 0 0 0 0.25rem rgba(123, 199, 77, 0.25);
}

/* Organization tabs and navigation */
[data-bs-theme="dark"] .org-tabs .nav-link {
    color: var(--org-text-color);
}

[data-bs-theme="dark"] .org-tabs .nav-link.active {
    background-color: var(--org-accent-color);
    color: #222831;
    border-color: var(--org-accent-color);
}

[data-bs-theme="dark"] .org-tabs .nav-link:hover:not(.active) {
    background-color: var(--org-hover-color);
    border-color: var(--org-border-color);
}

/* Organization tables */
[data-bs-theme="dark"] .org-table {
    color: var(--org-text-color);
    border-color: var(--org-border-color);
}

[data-bs-theme="dark"] .org-table th {
    background-color: rgba(123, 199, 77, 0.1);
    border-color: var(--org-border-color);
}

[data-bs-theme="dark"] .org-table td {
    border-color: var(--org-border-color);
}

[data-bs-theme="dark"] .org-table tr:hover {
    background-color: var(--org-hover-color);
}

/* Organization buttons */
[data-bs-theme="dark"] .btn-org-primary {
    background-color: var(--org-accent-color);
    border-color: var(--org-accent-color);
    color: #222831;
}

[data-bs-theme="dark"] .btn-org-primary:hover {
    background-color: #6AB33C;
    border-color: #6AB33C;
    color: #222831;
}

[data-bs-theme="dark"] .btn-org-outline {
    background-color: transparent;
    border-color: var(--org-accent-color);
    color: var(--org-accent-color);
}

[data-bs-theme="dark"] .btn-org-outline:hover {
    background-color: var(--org-accent-color);
    color: #222831;
}

/* Dark mode specific styles - enhanced */
[data-bs-theme="dark"] .navbar {
    background-color: #1a1f26 !important;
}

/* Target the logout link specifically */
[data-bs-theme="dark"] .nav-link[href="logout.php"],
[data-bs-theme="dark"] .list-group-item[href="logout.php"] {
    color: var(--color-third-darkest) !important;
}

[data-bs-theme="dark"] .nav-link[href="logout.php"]:hover,
[data-bs-theme="dark"] .list-group-item[href="logout.php"]:hover {
    color: #6AB33C !important; /* Slightly darker shade for hover */
}

[data-bs-theme="dark"] .post, 
[data-bs-theme="dark"] .card {
    background-color: #393E46;
    color: #EEEEEE;
}

[data-bs-theme="dark"] .card-header {
    background-color: var(--color-third-darkest) !important;
    color: white;
}

[data-bs-theme="dark"] .card-footer {
    background-color: var(--color-second-darkest) !important;
    color: white;
}

/* Target the commit input field */
[data-bs-theme="dark"] .commit-input,
[data-bs-theme="dark"] textarea[placeholder*="Write a comment"],
[data-bs-theme="dark"] input[placeholder*="Write a comment"],
[data-bs-theme="dark"] .comment-input {
    border: 2px solid var(--color-third-darkest);
    border-radius: 5px;
}

[data-bs-theme="dark"] .card-footer {
    background-color: #393E46 !important;
    /* color: var(--color-darkest); */
}


[data-bs-theme="dark"] .login-body {
    background-color: #393E46;
}

[data-bs-theme="dark"] input,
[data-bs-theme="dark"] textarea,
[data-bs-theme="dark"] select {
    background-color: #2c3037;
    color: #EEEEEE;
    border-color: rgba(238, 238, 238, 0.2);
}

[data-bs-theme="dark"] .form-control:focus {
    background-color: #2c3037;
    color: #EEEEEE;
}

[data-bs-theme="dark"] .dropdown-item {
    color: #EEEEEE;
}

[data-bs-theme="dark"] .dropdown-item:hover {
    background-color: #2c3037;
}

[data-bs-theme="dark"] .table {
    color: #EEEEEE;
}

[data-bs-theme="dark"] .modal-content {
    background-color: #393E46;
    color: #EEEEEE;
}

/* Profile page dark mode specific styles */
[data-bs-theme="dark"] .img-fluid.rounded-circle {
    border: 3px solid var(--color-third-darkest);
}

[data-bs-theme="dark"] .border-bottom {
    border-color: rgba(238, 238, 238, 0.2) !important;
}

[data-bs-theme="dark"] .nav-tabs {
    border-color: rgba(238, 238, 238, 0.2);
}

[data-bs-theme="dark"] .nav-tabs .nav-link.active {
    background-color: #2c3037;
    color: var(--color-third-darkest);
    border-color: rgba(238, 238, 238, 0.2);
}

[data-bs-theme="dark"] .nav-tabs .nav-link:not(.active) {
    color: #EEEEEE;
}

[data-bs-theme="dark"] .tab-content {
    background-color: #2c3037;
    border-radius: 0 0 5px 5px;
}

/* Ensure dark mode doesn't override our custom colors */
[data-bs-theme="dark"] .navbar-brand .brand-uni {
    color: var(--color-third-darkest);
}

[data-bs-theme="dark"] .navbar-brand .brand-link {
    color: var(--color-lightest);
}

/* Validation Page Styles */
.validation-card {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    border-radius: 0.5rem;
    overflow: hidden;
}

.validation-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.validation-table th {
    background-color: rgba(13, 110, 253, 0.05);
    font-weight: 600;
}

.validation-table tr:hover {
    background-color: rgba(123, 199, 77, 0.05);
}

.validation-badge {
    font-size: 0.8rem;
    padding: 0.35rem 0.65rem;
}

.validation-guidelines li {
    margin-bottom: 0.75rem;
    position: relative;
    padding-left: 1.5rem;
}

.validation-guidelines li:before {
    content: "\f058";
    font-family: "Font Awesome 5 Free";
    font-weight: 900;
    position: absolute;
    left: 0;
    color: #7BC74D;
}

.validation-actions .btn {
    transition: all 0.3s ease;
}

.validation-actions .btn:hover {
    transform: translateY(-2px);
}

.validation-stats {
    background-color: #f8f9fa;
    border-radius: 0.5rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.stat-card {
    text-align: center;
    padding: 1rem;
    border-radius: 0.5rem;
    background-color: white;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.stat-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    color: #7BC74D;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.stat-label {
    color: #6c757d;
    font-size: 0.9rem;
}

/* Alert styles for validation page */
.validation-page .alert {
    border-left: 4px solid;
}

.validation-page .alert-success {
    border-left-color: #7BC74D;
}

.validation-page .alert-danger {
    border-left-color: #dc3545;
}

.validation-page .alert-info {
    border-left-color: #0dcaf0;
}

.validation-page .alert-warning {
    border-left-color: #ffc107;
}

/* Dark mode specific styles for admin pages */
[data-bs-theme="dark"] .validation-card {
    background-color: var(--bs-card-bg);
    border-color: rgba(255, 255, 255, 0.1);
}

[data-bs-theme="dark"] .validation-table th {
    background-color: rgba(123, 199, 77, 0.1);
}

[data-bs-theme="dark"] .validation-table tr:hover {
    background-color: rgba(123, 199, 77, 0.1);
}

[data-bs-theme="dark"] .stat-card {
    background-color: var(--bs-card-bg);
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.2);
}

[data-bs-theme="dark"] .validation-stats {
    background-color: rgba(33, 37, 41, 0.5);
}

[data-bs-theme="dark"] .validation-guidelines li:before {
    color: #8fd458;
}

/* Dark mode theme toggle button styles */
.theme-icon-active {
    display: inline-block;
    width: 1em;
    height: 1em;
}

[data-bs-theme="dark"] .dropdown-menu {
    background-color: var(--bs-dropdown-bg);
    border-color: var(--bs-border-color);
}

[data-bs-theme="dark"] .dropdown-item {
    color: var(--bs-body-color);
}

[data-bs-theme="dark"] .dropdown-item:hover {
    background-color: var(--bs-dropdown-link-hover-bg);
}

[data-bs-theme="dark"] .dropdown-item.active {
    background-color: var(--bs-dropdown-link-active-bg);
}

/* Dark mode form styles */
[data-bs-theme="dark"] .form-control,
[data-bs-theme="dark"] .form-select {
    background-color: var(--bs-form-control-bg);
    color: var(--bs-form-control-color);
    border-color: var(--bs-border-color);
}

[data-bs-theme="dark"] .form-control:focus,
[data-bs-theme="dark"] .form-select:focus {
    border-color: #7BC74D;
    box-shadow: 0 0 0 0.25rem rgba(123, 199, 77, 0.25);
}

/* Dark mode modal styles */
[data-bs-theme="dark"] .modal-content {
    background-color: var(--bs-body-bg);
    border-color: var(--bs-border-color);
}

[data-bs-theme="dark"] .modal-header,
[data-bs-theme="dark"] .modal-footer {
    border-color: var(--bs-border-color);
}

/* Dark mode for the theme toggle button */
#bd-theme {
    z-index: 1;
}

/* Dark mode for the theme toggle dropdown */
.dropdown-menu-end {
    --bs-position: end;
}

/* Post image styling */
.post-image {
    margin: 15px 0;
    text-align: center;
}

.post-image img {
    max-width: 100%;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    max-height: 400px;
    object-fit: contain;
}

/* Post content styling */
.post-content {
    margin-bottom: 15px;
}

.read-more-link {
    color: #0d6efd;
    text-decoration: none;
    cursor: pointer;
    display: inline-block;
    margin-left: 5px;
}

.read-more-link:hover {
    text-decoration: underline;
}

/* Post images styling */
.post-images {
    margin: 15px 0;
}

.single-image img {
    width: 100%;
    max-height: 400px;
    object-fit: contain;
    border-radius: 8px;
    cursor: pointer;
    transition: opacity 0.3s;
}

.post-images .row img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 8px;
    cursor: pointer;
    transition: opacity 0.3s;
}

.clickable-image:hover {
    opacity: 0.9;
}

/* Image viewer modal */
.image-viewer-modal,
.image-viewer {
    display: none;
    position: fixed;
    z-index: 1050;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.9);
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.image-viewer-content {
    position: relative;
    width: 90%;
    max-width: 1200px;
    margin: auto;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
}

.close-viewer {
    position: absolute;
    top: 20px;
    right: 20px;
    color: white;
    font-size: 35px;
    font-weight: bold;
    cursor: pointer;
    z-index: 1060;
}

.viewer-image-container,
.image-viewer-body {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 80vh;
    width: 100%;
    position: relative;
}

.viewer-image,
#viewerImage {
    max-width: 90%;
    max-height: 80vh;
    object-fit: contain;
    position: relative;
    z-index: 1;
}

/* Enhanced image viewer styling with blur background */
.viewer-blur-bg,
.image-blur-bg,
#viewerBlurBg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    filter: blur(30px);
    opacity: 0.5;
    transform: scale(1.1);
    z-index: 0;
}

/* Image navigation buttons */
.image-navigation,
.nav-btn {
    position: absolute;
    z-index: 1060;
}

.image-navigation {
    width: 100%;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    justify-content: space-between;
    padding: 0 20px;
}

.nav-button,
.nav-btn {
    background-color: rgba(255, 255, 255, 0.3);
    color: white;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    font-size: 18px;
    cursor: pointer;
    transition: background-color 0.3s;
    display: flex;
    align-items: center;
    justify-content: center;
}

.prev-btn {
    left: 20px;
    top: 50%;
    transform: translateY(-50%);
}

.next-btn {
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
}

.nav-button:hover,
.nav-btn:hover {
    background-color: rgba(255, 255, 255, 0.5);
}

/* Image counter */
.image-counter,
#imageCounter {
    position: absolute;
    bottom: 20px;
    left: 0;
    right: 0;
    text-align: center;
    color: white;
    font-size: 14px;
    z-index: 1060;
}

/* Responsive adjustments for image viewer */
@media (max-width: 768px) {
    .viewer-image-container {
        height: 70vh;
    }
    
    .image-navigation {
        padding: 0 10px;
    }
    
    .nav-button {
        width: 36px;
        height: 36px;
    }
}

/* Photo grid styling */
.post-images .row {
    margin-right: -5px;
    margin-left: -5px;
}

.post-images .row > div {
    padding: 5px;
}

.post-images img {
    width: 100%;
    height: 600px;
    object-fit: cover;
    border-radius: 8px;
    cursor: pointer;
    transition: opacity 0.3s;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .post-images img {
        height: 150px;
    }
}

@media (max-width: 576px) {
    .post-images img {
        height: 120px;
    }
}

/* Clickable overlay styling */
.clickable-overlay {
    cursor: pointer;
    z-index: 10;
}

.clickable-overlay:hover {
    background-color: rgba(0,0,0,0.6) !important;
}

.clickable-overlay span {
    pointer-events: none; /* Ensures clicks pass through to the overlay */
}

/* Enhanced single image container with blur background */
.single-image {
    position: relative;
    width: 100%;
    max-height: 400px;
    border-radius: 8px;
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #f8f9fa;
}

/* Blur background for images */
.image-blur-bg {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    filter: blur(20px);
    opacity: 0.7;
    transform: scale(1.1); /* Prevent blur edges from showing */
    z-index: 0;
}

/* Image on top of blur */
.single-image img {
    position: relative;
    z-index: 1;
    max-width: 100%;
    max-height: 400px;
    object-fit: contain;
    border-radius: 4px;
    cursor: pointer;
    transition: opacity 0.3s;
}

/* For wide images */
.single-image.wide-image {
    height: 300px;
}

/* For tall images */
.single-image.tall-image {
    height: 400px;
}

/* For balanced images */
.single-image.balanced-image {
    height: auto;
    max-height: 400px;
}

/* Dropdown filter styling */
.dropdown-item.active {
    background-color: var(--bs-primary);
    color: white;
}

.dropdown-item:active {
    background-color: var(--bs-primary);
}

.btn-group .dropdown-toggle::after {
    margin-left: 0.5em;
}

/* Create Post Modal Styles */
.modal-header.bg-primary {
    color: white;
}

.photo-preview {
    min-height: 0;
    transition: min-height 0.3s ease;
}

.photo-preview.has-photos {
    min-height: 100px;
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
    padding: 10px;
    background-color: #f8f9fa;
}

#fb-preview-container {
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
    background-color: #f8f9fa;
    display: none;
}

/* Form validation styles */
.was-validated .form-control:invalid,
.form-control.is-invalid {
    border-color: #dc3545;
    padding-right: calc(1.5em + 0.75rem);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 12 12' width='12' height='12' fill='none' stroke='%23dc3545'%3e%3ccircle cx='6' cy='6' r='4.5'/%3e%3cpath stroke-linejoin='round' d='M5.8 3.6h.4L6 6.5z'/%3e%3ccircle cx='6' cy='8.2' r='.6' fill='%23dc3545' stroke='none'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

.was-validated .form-control:valid,
.form-control.is-valid {
    border-color: #198754;
    padding-right: calc(1.5em + 0.75rem);
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23198754' d='M2.3 6.73L.6 4.53c-.4-1.04.46-1.4 1.1-.8l1.1 1.4 3.4-3.8c.6-.63 1.6-.27 1.2.7l-4 4.6c-.43.5-.8.4-1.1.1z'/%3e%3c/svg%3e");
    background-repeat: no-repeat;
    background-position: right calc(0.375em + 0.1875rem) center;
    background-size: calc(0.75em + 0.375rem) calc(0.75em + 0.375rem);
}

/* Organization Dashboard Styles */
.org-header {
    background-color: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 20px;
}

.org-logo {
    width: 100px;
    height: 100px;
    object-fit: cover;
    border-radius: 50%;
}

.stat-card {
    text-align: center;
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 15px;
    background-color: #fff;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.stat-card i {
    font-size: 2rem;
    margin-bottom: 10px;
    color: #0d6efd;
}

.stat-card h3 {
    font-size: 1.5rem;
    margin-bottom: 5px;
}

.member-card {
    display: flex;
    align-items: center;
    padding: 10px;
    border-bottom: 1px solid #eee;
}

.member-card:last-child {
    border-bottom: none;
}

.member-pic {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 10px;
    object-fit: cover;
}

.post-card {
    border: 1px solid #eee;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 15px;
}

/* Dark mode overrides for organization dashboard */
[data-bs-theme="dark"] .stat-card {
    background-color: var(--bs-card-bg);
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.2);
}

[data-bs-theme="dark"] .member-card {
    border-bottom-color: rgba(255, 255, 255, 0.1);
}

[data-bs-theme="dark"] .post-card {
    border-color: rgba(255, 255, 255, 0.1);
    background-color: var(--bs-card-bg);
}

[data-bs-theme="dark"] .org-header {
    background-color: var(--bs-card-bg);
}

/* Organization-related dark mode styles */
[data-bs-theme="dark"] {
    /* Organization general styles */
    --org-bg-color: #393E46;
    --org-text-color: #EEEEEE;
    --org-border-color: rgba(238, 238, 238, 0.1);
    --org-hover-color: #2c3037;
    --org-accent-color: #7BC74D;
}

/* Organization cards in listings */
[data-bs-theme="dark"] .organization-card {
    background-color: var(--org-bg-color);
    border-color: var(--org-border-color);
    color: var(--org-text-color);
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.2);
}

[data-bs-theme="dark"] .organization-card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.3);
    border-color: var(--org-accent-color);
}

[data-bs-theme="dark"] .organization-card .card-title {
    color: var(--org-text-color);
}

[data-bs-theme="dark"] .organization-card .badge {
    background-color: var(--org-accent-color);
    color: #222831;
}

/* Organization dashboard */
[data-bs-theme="dark"] .org-header {
    background-color: var(--org-bg-color);
    border-color: var(--org-border-color);
}

[data-bs-theme="dark"] .org-logo {
    border: 3px solid var(--org-accent-color);
}

[data-bs-theme="dark"] .stat-card {
    background-color: var(--org-bg-color);
    border-color: var(--org-border-color);
    color: var(--org-text-color);
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.2);
}

[data-bs-theme="dark"] .stat-card i {
    color: var(--org-accent-color);
}

/* Organization members */
[data-bs-theme="dark"] .member-card {
    background-color: var(--org-bg-color);
    border-bottom-color: var(--org-border-color);
}

[data-bs-theme="dark"] .member-card:hover {
    background-color: var(--org-hover-color);
}

[data-bs-theme="dark"] .member-pic {
    border: 2px solid var(--org-accent-color);
}

/* Organization posts */
[data-bs-theme="dark"] .post-card {
    background-color: var(--org-bg-color);
    border-color: var(--org-border-color);
    color: var(--org-text-color);
}

[data-bs-theme="dark"] .post-card .post-header {
    border-bottom-color: var(--org-border-color);
}

[data-bs-theme="dark"] .post-card .post-footer {
    border-top-color: var(--org-border-color);
}

/* Organization events */
[data-bs-theme="dark"] .event-card {
    background-color: var(--org-bg-color);
    border-color: var(--org-border-color);
    color: var(--org-text-color);
}

[data-bs-theme="dark"] .event-card .event-date {
    background-color: var(--org-accent-color);
    color: #222831;
}

[data-bs-theme="dark"] .event-card:hover {
    background-color: var(--org-hover-color);
    border-color: var(--org-accent-color);
}

/* Organization forms */
[data-bs-theme="dark"] .org-form {
    background-color: var(--org-bg-color);
    border-color: var(--org-border-color);
    color: var(--org-text-color);
}

[data-bs-theme="dark"] .org-form .form-control,
[data-bs-theme="dark"] .org-form .form-select {
    background-color: #2c3037;
    border-color: var(--org-border-color);
    color: var(--org-text-color);
}

[data-bs-theme="dark"] .org-form .form-control:focus,
[data-bs-theme="dark"] .org-form .form-select:focus {
    border-color: var(--org-accent-color);
    box-shadow: 0 0 0 0.25rem rgba(123, 199, 77, 0.25);
}

/* Organization tabs and navigation */
[data-bs-theme="dark"] .org-tabs .nav-link {
    color: var(--org-text-color);
}

[data-bs-theme="dark"] .org-tabs .nav-link.active {
    background-color: var(--org-accent-color);
    color: #222831;
    border-color: var(--org-accent-color);
}

[data-bs-theme="dark"] .org-tabs .nav-link:hover:not(.active) {
    background-color: var(--org-hover-color);
    border-color: var(--org-border-color);
}

/* Organization tables */
[data-bs-theme="dark"] .org-table {
    color: var(--org-text-color);
    border-color: var(--org-border-color);
}

[data-bs-theme="dark"] .org-table th {
    background-color: rgba(123, 199, 77, 0.1);
    border-color: var(--org-border-color);
}

[data-bs-theme="dark"] .org-table td {
    border-color: var(--org-border-color);
}

[data-bs-theme="dark"] .org-table tr:hover {
    background-color: var(--org-hover-color);
}

/* Organization buttons */
[data-bs-theme="dark"] .btn-org-primary {
    background-color: var(--org-accent-color);
    border-color: var(--org-accent-color);
    color: #222831;
}

[data-bs-theme="dark"] .btn-org-primary:hover {
    background-color: #6AB33C;
    border-color: #6AB33C;
    color: #222831;
}

[data-bs-theme="dark"] .btn-org-outline {
    background-color: transparent;
    border-color: var(--org-accent-color);
    color: var(--org-accent-color);
}

[data-bs-theme="dark"] .btn-org-outline:hover {
    background-color: var(--org-accent-color);
    color: #222831;
}

/* Organization styles */
.org-logo {
    width: 100px;
    height: 100px;
    object-fit: cover;
    border-radius: 50%;
    /* margin: 0 auto; */
    border: 3px solid #7BC74D;
}

.stat-card {
    background-color: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    transition: all 0.3s ease;
    height: 90%;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.stat-card i {
    font-size: 2.5rem;
    color: #7BC74D;
    margin-bottom: 10px;
}

.stat-card h3 {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.stat-card p {
    color: #6c757d;
    margin-bottom: 0;
}

.btn-org-primary {
    background-color: #7BC74D;
    border-color: #7BC74D;
    color: #fff;
}

.btn-org-primary:hover {
    background-color: #6ab33e;
    border-color: #6ab33e;
    color: #fff;
}

.btn-org-outline {
    background-color: transparent;
    border-color: #7BC74D;
    color: #7BC74D;
}

.btn-org-outline:hover {
    background-color: #7BC74D;
    color: #fff;
}

.member-pic {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

/* Dark mode styles */
[data-bs-theme="dark"] .stat-card {
    background-color: #343a40;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
}

[data-bs-theme="dark"] .stat-card p {
    color: #adb5bd;
}

[data-bs-theme="dark"] .btn-org-primary {
    background-color: #7BC74D;
    border-color: #7BC74D;
    color: #212529;
}

[data-bs-theme="dark"] .btn-org-primary:hover {
    background-color: #6ab33e;
    border-color: #6ab33e;
    color: #212529;
}

[data-bs-theme="dark"] .btn-org-outline {
    background-color: transparent;
    border-color: #7BC74D;
    color: #7BC74D;
}

[data-bs-theme="dark"] .btn-org-outline:hover {
    background-color: #7BC74D;
    color: #212529;
}

[data-bs-theme="dark"] .member-pic {
    border: 2px solid #7BC74D;
}

[data-bs-theme="dark"] .org-logo {
    border-color: #7BC74D;
    box-shadow: 0 0 10px rgba(123, 199, 77, 0.5);
}

/* Reaction system styles */
.reaction-container {
    position: relative;
    display: inline-block;
}

.reaction-options {
    position: absolute;
    bottom: 40px;
    left: 0;
    background: white;
    border-radius: 30px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    display: none;
    padding: 5px 10px;
    z-index: 100;
    white-space: nowrap;
    transition: opacity 0.3s;
    opacity: 0;
    /* Add flex display for horizontal layout */
    display: none;
    flex-direction: row;
    align-items: center;
}

[data-bs-theme="dark"] .reaction-options {
    background: #343a40;
}

.reaction-container:hover .reaction-options {
    display: block;
    opacity: 1;
}

.reaction-options.show {
    display: flex;
    opacity: 1;
}

.reaction-option {
    padding: 5px 8px;
    cursor: pointer;
    transition: transform 0.2s ease;
    display: inline-flex;
    flex-direction: column;
    align-items: center;
    font-size: 0.7rem;
    position: relative;
}

.reaction-option i {
    font-size: 1.2rem;
    margin-bottom: 2px;
}

.reaction-option:hover {
    transform: scale(1.3);
}

.reaction-option span {
    position: absolute;
    top: -25px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0,0,0,0.7);
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.65rem;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.2s, visibility 0.2s;
}

.reaction-option:hover span {
    opacity: 1;
    visibility: visible;
}

/* Reaction colors */
.reaction-like i { color: #2078f4; }
.reaction-love i { color: #f33e58; }
.reaction-care i { color: #f7b125; }
.reaction-haha i { color: #f7b125; }
.reaction-wow i { color: #f7b125; }
.reaction-sad i { color: #f7b125; }
.reaction-angry i { color: #e9710f; }

/* Selected reaction button styles */
.btn.reaction-like { color: #2078f4; border-color: #2078f4; }
.btn.reaction-love { color: #f33e58; border-color: #f33e58; }
.btn.reaction-care { color: #f7b125; border-color: #f7b125; }
.btn.reaction-haha { color: #f7b125; border-color: #f7b125; }
.btn.reaction-wow { color: #f7b125; border-color: #f7b125; }
.btn.reaction-sad { color: #f7b125; border-color: #f7b125; }
.btn.reaction-angry { color: #e9710f; border-color: #e9710f; }

/* Scrollable containers */
.posts-scrollable-container {
    max-height: 600px;
    overflow-y: auto;
    scrollbar-width: thin; /* For Firefox */
}

.posts-scrollable-container::-webkit-scrollbar {
    width: 6px;
}

.posts-scrollable-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

.posts-scrollable-container::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 10px;
}

.posts-scrollable-container::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* Dark mode scrollbar */
[data-bs-theme="dark"] .posts-scrollable-container::-webkit-scrollbar-track {
    background: #2b3035;
}

[data-bs-theme="dark"] .posts-scrollable-container::-webkit-scrollbar-thumb {
    background: #666;
}

[data-bs-theme="dark"] .posts-scrollable-container::-webkit-scrollbar-thumb:hover {
    background: #888;
}
