<?php
// Include admin authentication check
require_once '../includes/admin_check.php';

// Database connection
$conn = mysqli_connect("localhost", "root", "", "unilink_db");

// Check connection
if (!$conn) {
    die("Connection failed: " . mysqli_connect_error());
}

$admin_id = $_SESSION['user_id'];

// Get admin posts
$posts_query = "SELECT p.*, u.username 
                FROM posts p 
                JOIN users u ON p.user_id = u.id 
                WHERE p.user_id = '$admin_id' 
                ORDER BY p.created_at DESC";
$posts_result = mysqli_query($conn, $posts_query);
$posts = [];
if ($posts_result) {
    $posts = mysqli_fetch_all($posts_result, MYSQLI_ASSOC);
}

// Get all organizations for the create post modal
$all_orgs_query = "SELECT id, name FROM organizations ORDER BY name";
$all_orgs_result = mysqli_query($conn, $all_orgs_query);
$all_orgs = [];
if ($all_orgs_result) {
    $all_orgs = mysqli_fetch_all($all_orgs_result, MYSQLI_ASSOC);
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Posts - Admin Dashboard</title>
    <!-- Bootstrap CSS -->
    <link href="../assets/libraries/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="../assets/libraries/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="../assets/libraries/fontawesome/css/all.min.css">
    <!-- Custom CSS -->
    <link href="../assets/css/style.css" rel="stylesheet">
    <style>
        .post-card {
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 16px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        .post-preview {
            max-height: 100px;
            overflow: hidden;
        }
        .post-full-content {
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand fw-bold" href="../index.php">
                <span style="color: #7BC74D;">Uni</span><span style="color: #EEEEEE;">Link</span>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.php">
                            <i class="fas fa-tachometer-alt me-1"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="admin_posts.php">
                            <i class="fas fa-clipboard-list me-1"></i> My Posts
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="manage_org_posts.php">
                            <i class="fas fa-building me-1"></i> Organization Posts
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-shield me-1"></i> <?php echo htmlspecialchars($_SESSION['username']); ?>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="dashboard.php">
                                <i class="fas fa-tachometer-alt me-1"></i> Dashboard
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="../logout.php">
                                <i class="fas fa-sign-out-alt me-1"></i> Logout
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container py-4">
        <div class="row">
            <div class="col-md-4">
                <!-- Admin Info -->
                <div class="card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-user-shield me-2"></i>Admin Profile</h5>
                    </div>
                    <div class="card-body">
                        <div class="text-center mb-3">
                            <img src="../assets/img/admin_avatar.jpg" class="rounded-circle" width="100" height="100" alt="Admin Avatar">
                            <h5 class="mt-2 mb-0"><?php echo htmlspecialchars($_SESSION['username']); ?></h5>
                            <p class="text-muted">Administrator</p>
                        </div>
                    </div>
                </div>

                <!-- Quick Links -->
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-link me-2"></i>Quick Links</h5>
                    </div>
                    <div class="card-body">
                        <div class="list-group">
                            <a href="../create_post.php" class="list-group-item list-group-item-action">
                                <i class="fas fa-plus-circle me-2"></i> Create New Post
                            </a>
                            <a href="dashboard.php" class="list-group-item list-group-item-action">
                                <i class="fas fa-tachometer-alt me-2"></i> Admin Dashboard
                            </a>
                            <a href="manage_org_posts.php" class="list-group-item list-group-item-action">
                                <i class="fas fa-clipboard-list me-2"></i> Manage Organization Posts
                            </a>
                            <a href="../organization.php" class="list-group-item list-group-item-action">
                                <i class="fas fa-building me-2"></i> Manage Organizations
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Posts -->
            <div class="col-md-8">
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-clipboard me-2"></i>My Posts</h5>
                        <a href="../create_post.php" class="btn btn-sm btn-light">
                            <i class="fas fa-plus-circle me-1"></i>New Post
                        </a>
                    </div>
                    <div class="card-body p-0">
                        <div class="posts-scrollable-container" style="max-height: 838px; overflow-y: auto; padding: 1rem;">
                            <?php if(count($posts) > 0): ?>
                                <?php foreach($posts as $post): ?>
                                    <div class="post-card mb-4">
                                        <div class="post-header d-flex justify-content-between align-items-center">
                                            <div class="d-flex align-items-center">
                                                <img src="../assets/img/admin_avatar.jpg" class="rounded-circle me-2" width="40" height="40" alt="Admin Avatar">
                                                <div>
                                                    <h6 class="mb-0">Admin</h6>
                                                    <small class="text-muted">
                                                        Posted <?php echo date('M d, Y g:i A', strtotime($post['created_at'])); ?>
                                                    </small>
                                                </div>
                                            </div>
                                            <div class="dropdown">
                                                <button class="btn" type="button" data-bs-toggle="dropdown">
                                                    <i class="fas fa-ellipsis-v"></i>
                                                </button>
                                                <ul class="dropdown-menu dropdown-menu-end">
                                                    <li><a class="dropdown-item" href="javascript:void(0)" onclick="editPost(<?php echo $post['id']; ?>); return false;">
                                                        <i class="fas fa-edit me-2"></i>Edit
                                                    </a></li>
                                                    <li><a class="dropdown-item" href="javascript:void(0)" onclick="confirmDeletePost(<?php echo $post['id']; ?>); return false;">
                                                        <i class="fas fa-trash-alt me-2" style="color: red;"></i>Delete
                                                    </a></li>
                                                </ul>
                                            </div>
                                        </div>
                                        <div class="post-content mt-3">
                                            <h5><?php echo htmlspecialchars($post['title']); ?></h5>
                                            <div class="post-text">
                                                <?php 
                                                $description = htmlspecialchars($post['description']);
                                                if (strlen($description) > 300) {
                                                    echo '<div class="post-preview">' . substr($description, 0, 300) . '... <a href="#" class="read-more" onclick="toggleReadMore(this, event)">Read more</a></div>';
                                                    echo '<div class="post-full-content d-none">' . $description . ' <a href="#" class="read-less" onclick="toggleReadMore(this, event)">Read less</a></div>';
                                                } else {
                                                    echo '<div>' . $description . '</div>';
                                                }
                                                ?>
                                            </div>
                                            
                                            <div class="post-footer mt-3 d-flex justify-content-between">
                                                <div class="post-stats">
                                                    <span class="me-3"><i class="far fa-eye me-1"></i><?php echo rand(10, 500); ?></span>
                                                    <span><i class="far fa-comment me-1"></i><?php echo rand(0, 50); ?></span>
                                                </div>
                                                <div>
                                                    <a href="../view_post.php?id=<?php echo $post['id']; ?>" class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-external-link-alt me-1"></i>View Details
                                                    </a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <div class="text-center py-5">
                                    <i class="fas fa-clipboard-list fa-3x text-muted mb-3"></i>
                                    <h5>No Posts Yet</h5>
                                    <p class="text-muted">Create your first post to share with the community</p>
                                    <a href="../create_post.php" class="btn btn-primary mt-2">
                                        <i class="fas fa-plus-circle me-2"></i>Create Post
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Post Confirmation Modal -->
    <div class="modal fade" id="deletePostModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header bg-danger text-white">
                    <h5 class="modal-title"><i class="fas fa-exclamation-triangle me-2"></i>Delete Post</h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <p>Are you sure you want to delete this post? This action cannot be undone.</p>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <a href="#" id="confirmDeleteBtn" class="btn btn-danger">Delete</a>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="../assets/libraries/js/bootstrap.bundle.min.js"></script>
    <!-- Include the dark mode script -->
    <script src="../assets/js/main.js"></script>
    
    <script>
        function toggleReadMore(element, event) {
            event.preventDefault();
            const postContent = element.closest('.post-text');
            const preview = postContent.querySelector('.post-preview');
            const fullContent = postContent.querySelector('.post-full-content');
            
            if (preview.classList.contains('d-none')) {
                preview.classList.remove('d-none');
                fullContent.classList.add('d-none');
            } else {
                preview.classList.add('d-none');
                fullContent.classList.remove('d-none');
            }
        }
        
        function editPost(postId) {
            window.location.href = '../edit_post.php?id=' + postId;
        }
        
        function confirmDeletePost(postId) {
            const deleteModal = new bootstrap.Modal(document.getElementById('deletePostModal'));
            const confirmBtn = document.getElementById('confirmDeleteBtn');
            confirmBtn.href = '../delete_post.php?id=' + postId + '&redirect=admin';
            deleteModal.show();
        }
    </script>
</body>
</html>