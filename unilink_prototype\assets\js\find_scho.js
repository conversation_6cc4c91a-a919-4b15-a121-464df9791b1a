const scholarships = [
    { name: "Academic Excellence Scholarship", description: "For students with outstanding academic achievements.", image: "img/img1.png"},
    { name: "Sports Scholarship", description: "For students excelling in athletics.", image: "img/img1.png"},
    { name: "Artistic Talent Grant", description: "For students demonstrating exceptional talent in arts.", image: "img/img1.png" },
    { name: "Community Service Award", description: "For students contributing to their communities.", image: "img/img1.png"},
    { name: "Financial Aid Scholarship", description: "For students in need of financial assistance.", image: "img/img1.png"},
    { name: "STEM Innovation Scholarship", description: "For students pursuing degrees in Science, Technology, Engineering, or Mathematics.", image: "img/img1.png" },
    { name: "International Student Scholarship", description: "For students from overseas with strong academic records.", image: "img/img1.png" },
    { name: "Diversity Scholarship", description: "For students from diverse backgrounds or underrepresented groups.", image: "img/img1.png" },
    { name: "Leadership Excellence Scholarship", description: "For students who have demonstrated leadership in student organizations or community service.", image: "img/img1.png" },
    { name: "Environmental Sustainability Grant", description: "For students engaged in environmental conservation and sustainability efforts.", image: "img/img1.png" },
    { name: "Healthcare Heroes Scholarship", description: "For students pursuing careers in healthcare or public health.", image: "img/img1.png" },
    { name: "Future Educators Scholarship", description: "For students committed to becoming teachers or educators.", image: "img/img1.png" },
    { name: "Innovation in Technology Grant", description: "For students working on groundbreaking technology projects.", image: "img/img1.png" },
    { name: "Entrepreneurial Spirit Scholarship", description: "For students launching or managing their own businesses.", image: "img/img1.png" },
    { name: "Performing Arts Fellowship", description: "For students excelling in music, dance, or theater.", image: "img/img1.png" },
    { name: "Military Family Scholarship", description: "For students with family members in the armed forces.", image: "img/img1.png" },
    { name: "Women's Leadership Grant", description: "For female students demonstrating leadership potential.", image: "img/img1.png" },
    { name: "Rural Students Scholarship", description: "For students from rural areas pursuing higher education.", image: "img/img1.png" },
    { name: "First-Generation Scholarship", description: "For first-generation college students.", image: "img/img1.png" },
    { name: "Social Impact Award", description: "For students dedicated to solving social challenges.", image: "img/img1.png" }

    // Add more scholarships with corresponding images
];

// Function to display all scholarships
function displayScholarships() {
    scholarshipList.innerHTML = '';
    scholarships.forEach((scholarship) => {
        const li = document.createElement('li');
        li.innerHTML = `
            <img src="${scholarship.image}" alt="${scholarship.name}">
            <div class="overlay">
                <strong>${scholarship.name}</strong>
                <span>${scholarship.description}</span>
            </div>
        `;
        scholarshipList.appendChild(li);
    });
}

function filterScholarships() {
    const searchInput = document.getElementById('searchInput').value.toLowerCase();
    const filtered = scholarships.filter(scholarship =>
        scholarship.name.toLowerCase().includes(searchInput) || 
        scholarship.description.toLowerCase().includes(searchInput)
    );

    scholarshipList.innerHTML = '';
    if (filtered.length === 0) {
        scholarshipList.innerHTML = '<li style="color: var(--color-darkest); text-align: center;">No scholarships found</li>';
    } else {
        filtered.forEach((scholarship) => {
            const li = document.createElement('li');
            li.innerHTML = `
                <img src="${scholarship.image}" alt="${scholarship.name}">
                <div class="overlay">
                    <strong>${scholarship.name}</strong><br>${scholarship.description}
                </div>`;
            scholarshipList.appendChild(li);
        });
    }
}

// Initialize the list on page load
displayScholarships();