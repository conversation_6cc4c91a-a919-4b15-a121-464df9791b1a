import { useState, useEffect } from 'react';
import { Head, Link, router } from '@inertiajs/react';
import AuthenticatedLayout from '@/layouts/authenticated-layout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Search, Plus, Users, FileText } from 'lucide-react';

interface Organization {
    id: number;
    name: string;
    description: string;
    type: string;
    campus: string;
    status: string;
    logo: string | null;
    members_count: number;
    posts_count: number;
    created_at: string;
}

interface OrganizationsData {
    data: Organization[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
}

export default function OrganizationsIndex() {
    const [organizations, setOrganizations] = useState<OrganizationsData | null>(null);
    const [loading, setLoading] = useState(true);
    const [search, setSearch] = useState('');
    const [filter, setFilter] = useState({
        type: '',
        campus: '',
        status: 'active'
    });

    const fetchOrganizations = async (page = 1) => {
        setLoading(true);
        try {
            const params = new URLSearchParams({
                page: page.toString(),
                per_page: '12',
                ...(search && { search }),
                ...(filter.type && { type: filter.type }),
                ...(filter.campus && { campus: filter.campus }),
                ...(filter.status && { status: filter.status }),
            });

            const response = await fetch(`/api/v1/organizations?${params}`);
            const data = await response.json();
            setOrganizations(data);
        } catch (error) {
            console.error('Error fetching organizations:', error);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        fetchOrganizations();
    }, [search, filter]);

    const handleSearch = (e: React.FormEvent) => {
        e.preventDefault();
        fetchOrganizations();
    };

    const getStatusColor = (status: string) => {
        switch (status) {
            case 'active': return 'bg-green-100 text-green-800';
            case 'pending': return 'bg-yellow-100 text-yellow-800';
            case 'inactive': return 'bg-gray-100 text-gray-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    };

    const getTypeColor = (type: string) => {
        switch (type.toLowerCase()) {
            case 'club': return 'bg-blue-100 text-blue-800';
            case 'society': return 'bg-purple-100 text-purple-800';
            case 'department': return 'bg-indigo-100 text-indigo-800';
            default: return 'bg-gray-100 text-gray-800';
        }
    };

    return (
        <AuthenticatedLayout>
            <Head title="Organizations" />

            <div className="py-12">
                <div className="max-w-7xl mx-auto sm:px-6 lg:px-8">
                    <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div className="p-6 text-gray-900">
                            {/* Header */}
                            <div className="flex justify-between items-center mb-6">
                                <div>
                                    <h1 className="text-3xl font-bold text-gray-900">Organizations</h1>
                                    <p className="text-gray-600 mt-1">Discover and join university organizations</p>
                                </div>
                                <Link href="/organizations/create">
                                    <Button>
                                        <Plus className="w-4 h-4 mr-2" />
                                        Create Organization
                                    </Button>
                                </Link>
                            </div>

                            {/* Search and Filters */}
                            <div className="mb-6 space-y-4">
                                <form onSubmit={handleSearch} className="flex gap-4">
                                    <div className="flex-1">
                                        <div className="relative">
                                            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                                            <Input
                                                type="text"
                                                placeholder="Search organizations..."
                                                value={search}
                                                onChange={(e) => setSearch(e.target.value)}
                                                className="pl-10"
                                            />
                                        </div>
                                    </div>
                                    <Button type="submit">Search</Button>
                                </form>

                                <div className="flex gap-4">
                                    <select
                                        value={filter.type}
                                        onChange={(e) => setFilter({ ...filter, type: e.target.value })}
                                        className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    >
                                        <option value="">All Types</option>
                                        <option value="club">Club</option>
                                        <option value="society">Society</option>
                                        <option value="department">Department</option>
                                    </select>

                                    <select
                                        value={filter.status}
                                        onChange={(e) => setFilter({ ...filter, status: e.target.value })}
                                        className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                    >
                                        <option value="">All Status</option>
                                        <option value="active">Active</option>
                                        <option value="pending">Pending</option>
                                        <option value="inactive">Inactive</option>
                                    </select>
                                </div>
                            </div>

                            {/* Organizations Grid */}
                            {loading ? (
                                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                    {[...Array(6)].map((_, i) => (
                                        <Card key={i} className="animate-pulse">
                                            <CardHeader>
                                                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                                                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                                            </CardHeader>
                                            <CardContent>
                                                <div className="h-3 bg-gray-200 rounded w-full mb-2"></div>
                                                <div className="h-3 bg-gray-200 rounded w-2/3"></div>
                                            </CardContent>
                                        </Card>
                                    ))}
                                </div>
                            ) : organizations?.data.length === 0 ? (
                                <div className="text-center py-12">
                                    <div className="text-gray-400 mb-4">
                                        <Users className="w-16 h-16 mx-auto" />
                                    </div>
                                    <h3 className="text-lg font-medium text-gray-900 mb-2">No organizations found</h3>
                                    <p className="text-gray-600 mb-4">Try adjusting your search criteria or create a new organization.</p>
                                    <Link href="/organizations/create">
                                        <Button>Create Organization</Button>
                                    </Link>
                                </div>
                            ) : (
                                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                    {organizations?.data.map((org) => (
                                        <Card key={org.id} className="hover:shadow-lg transition-shadow cursor-pointer">
                                            <Link href={`/organizations/${org.id}`}>
                                                <CardHeader>
                                                    <div className="flex items-start justify-between">
                                                        <div className="flex-1">
                                                            <CardTitle className="text-lg">{org.name}</CardTitle>
                                                            <CardDescription className="mt-1">
                                                                {org.campus && `${org.campus} • `}
                                                                {new Date(org.created_at).getFullYear()}
                                                            </CardDescription>
                                                        </div>
                                                        {org.logo && (
                                                            <img
                                                                src={`/storage/${org.logo}`}
                                                                alt={`${org.name} logo`}
                                                                className="w-12 h-12 rounded-lg object-cover"
                                                            />
                                                        )}
                                                    </div>
                                                </CardHeader>
                                                <CardContent>
                                                    <p className="text-sm text-gray-600 mb-4 line-clamp-2">
                                                        {org.description || 'No description available.'}
                                                    </p>
                                                    
                                                    <div className="flex flex-wrap gap-2 mb-4">
                                                        <Badge className={getTypeColor(org.type)}>
                                                            {org.type}
                                                        </Badge>
                                                        <Badge className={getStatusColor(org.status)}>
                                                            {org.status}
                                                        </Badge>
                                                    </div>

                                                    <div className="flex items-center justify-between text-sm text-gray-500">
                                                        <div className="flex items-center">
                                                            <Users className="w-4 h-4 mr-1" />
                                                            {org.members_count} members
                                                        </div>
                                                        <div className="flex items-center">
                                                            <FileText className="w-4 h-4 mr-1" />
                                                            {org.posts_count} posts
                                                        </div>
                                                    </div>
                                                </CardContent>
                                            </Link>
                                        </Card>
                                    ))}
                                </div>
                            )}

                            {/* Pagination */}
                            {organizations && organizations.last_page > 1 && (
                                <div className="mt-8 flex justify-center">
                                    <div className="flex gap-2">
                                        {Array.from({ length: organizations.last_page }, (_, i) => i + 1).map((page) => (
                                            <Button
                                                key={page}
                                                variant={page === organizations.current_page ? "default" : "outline"}
                                                size="sm"
                                                onClick={() => fetchOrganizations(page)}
                                            >
                                                {page}
                                            </Button>
                                        ))}
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            </div>
        </AuthenticatedLayout>
    );
}
