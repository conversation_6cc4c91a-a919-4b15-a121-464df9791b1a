import { useState } from 'react';
import { Head, router } from '@inertiajs/react';
import AuthenticatedLayout from '@/layouts/authenticated-layout';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { ArrowLeft, Upload, AlertCircle } from 'lucide-react';

interface FormData {
    name: string;
    description: string;
    type: string;
    email: string;
    phone: string;
    website: string;
    address: string;
    campus: string;
    logo: File | null;
    banner_image: File | null;
}

interface FormErrors {
    [key: string]: string[];
}

export default function CreateOrganization() {
    const [formData, setFormData] = useState<FormData>({
        name: '',
        description: '',
        type: 'club',
        email: '',
        phone: '',
        website: '',
        address: '',
        campus: '',
        logo: null,
        banner_image: null,
    });

    const [errors, setErrors] = useState<FormErrors>({});
    const [loading, setLoading] = useState(false);

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
        const { name, value } = e.target;
        setFormData(prev => ({ ...prev, [name]: value }));
        
        // Clear error when user starts typing
        if (errors[name]) {
            setErrors(prev => ({ ...prev, [name]: [] }));
        }
    };

    const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const { name, files } = e.target;
        if (files && files[0]) {
            setFormData(prev => ({ ...prev, [name]: files[0] }));
            
            // Clear error when user selects file
            if (errors[name]) {
                setErrors(prev => ({ ...prev, [name]: [] }));
            }
        }
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setLoading(true);
        setErrors({});

        try {
            const submitData = new FormData();
            
            // Append all form fields
            Object.entries(formData).forEach(([key, value]) => {
                if (value !== null && value !== '') {
                    if (value instanceof File) {
                        submitData.append(key, value);
                    } else {
                        submitData.append(key, value.toString());
                    }
                }
            });

            const response = await fetch('/api/v1/organizations', {
                method: 'POST',
                body: submitData,
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                },
            });

            const result = await response.json();

            if (response.ok) {
                router.visit(`/organizations/${result.organization.id}`, {
                    onSuccess: () => {
                        // Show success message
                    }
                });
            } else {
                if (result.errors) {
                    setErrors(result.errors);
                } else {
                    setErrors({ general: [result.message || 'An error occurred'] });
                }
            }
        } catch (error) {
            console.error('Error creating organization:', error);
            setErrors({ general: ['An unexpected error occurred. Please try again.'] });
        } finally {
            setLoading(false);
        }
    };

    return (
        <AuthenticatedLayout>
            <Head title="Create Organization" />

            <div className="py-12">
                <div className="max-w-4xl mx-auto sm:px-6 lg:px-8">
                    <div className="bg-white overflow-hidden shadow-sm sm:rounded-lg">
                        <div className="p-6 text-gray-900">
                            {/* Header */}
                            <div className="flex items-center mb-6">
                                <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => router.visit('/organizations')}
                                    className="mr-4"
                                >
                                    <ArrowLeft className="w-4 h-4 mr-2" />
                                    Back to Organizations
                                </Button>
                                <div>
                                    <h1 className="text-3xl font-bold text-gray-900">Create Organization</h1>
                                    <p className="text-gray-600 mt-1">Start a new organization to connect with fellow students</p>
                                </div>
                            </div>

                            {/* General Error Alert */}
                            {errors.general && (
                                <Alert className="mb-6 border-red-200 bg-red-50">
                                    <AlertCircle className="h-4 w-4 text-red-600" />
                                    <AlertDescription className="text-red-800">
                                        {errors.general[0]}
                                    </AlertDescription>
                                </Alert>
                            )}

                            <form onSubmit={handleSubmit} className="space-y-6">
                                {/* Basic Information */}
                                <Card>
                                    <CardHeader>
                                        <CardTitle>Basic Information</CardTitle>
                                        <CardDescription>
                                            Provide the essential details about your organization
                                        </CardDescription>
                                    </CardHeader>
                                    <CardContent className="space-y-4">
                                        <div>
                                            <Label htmlFor="name">Organization Name *</Label>
                                            <Input
                                                id="name"
                                                name="name"
                                                type="text"
                                                value={formData.name}
                                                onChange={handleInputChange}
                                                placeholder="Enter organization name"
                                                className={errors.name ? 'border-red-500' : ''}
                                                required
                                            />
                                            {errors.name && (
                                                <p className="text-red-500 text-sm mt-1">{errors.name[0]}</p>
                                            )}
                                        </div>

                                        <div>
                                            <Label htmlFor="description">Description</Label>
                                            <Textarea
                                                id="description"
                                                name="description"
                                                value={formData.description}
                                                onChange={handleInputChange}
                                                placeholder="Describe your organization's mission and activities"
                                                rows={4}
                                                className={errors.description ? 'border-red-500' : ''}
                                            />
                                            {errors.description && (
                                                <p className="text-red-500 text-sm mt-1">{errors.description[0]}</p>
                                            )}
                                        </div>

                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            <div>
                                                <Label htmlFor="type">Organization Type *</Label>
                                                <select
                                                    id="type"
                                                    name="type"
                                                    value={formData.type}
                                                    onChange={handleInputChange}
                                                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                                    required
                                                >
                                                    <option value="club">Club</option>
                                                    <option value="society">Society</option>
                                                    <option value="department">Department</option>
                                                    <option value="committee">Committee</option>
                                                    <option value="other">Other</option>
                                                </select>
                                                {errors.type && (
                                                    <p className="text-red-500 text-sm mt-1">{errors.type[0]}</p>
                                                )}
                                            </div>

                                            <div>
                                                <Label htmlFor="campus">Campus</Label>
                                                <Input
                                                    id="campus"
                                                    name="campus"
                                                    type="text"
                                                    value={formData.campus}
                                                    onChange={handleInputChange}
                                                    placeholder="Main Campus, North Campus, etc."
                                                    className={errors.campus ? 'border-red-500' : ''}
                                                />
                                                {errors.campus && (
                                                    <p className="text-red-500 text-sm mt-1">{errors.campus[0]}</p>
                                                )}
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>

                                {/* Contact Information */}
                                <Card>
                                    <CardHeader>
                                        <CardTitle>Contact Information</CardTitle>
                                        <CardDescription>
                                            How can people reach your organization?
                                        </CardDescription>
                                    </CardHeader>
                                    <CardContent className="space-y-4">
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            <div>
                                                <Label htmlFor="email">Email</Label>
                                                <Input
                                                    id="email"
                                                    name="email"
                                                    type="email"
                                                    value={formData.email}
                                                    onChange={handleInputChange}
                                                    placeholder="<EMAIL>"
                                                    className={errors.email ? 'border-red-500' : ''}
                                                />
                                                {errors.email && (
                                                    <p className="text-red-500 text-sm mt-1">{errors.email[0]}</p>
                                                )}
                                            </div>

                                            <div>
                                                <Label htmlFor="phone">Phone</Label>
                                                <Input
                                                    id="phone"
                                                    name="phone"
                                                    type="tel"
                                                    value={formData.phone}
                                                    onChange={handleInputChange}
                                                    placeholder="+****************"
                                                    className={errors.phone ? 'border-red-500' : ''}
                                                />
                                                {errors.phone && (
                                                    <p className="text-red-500 text-sm mt-1">{errors.phone[0]}</p>
                                                )}
                                            </div>
                                        </div>

                                        <div>
                                            <Label htmlFor="website">Website</Label>
                                            <Input
                                                id="website"
                                                name="website"
                                                type="url"
                                                value={formData.website}
                                                onChange={handleInputChange}
                                                placeholder="https://yourorganization.com"
                                                className={errors.website ? 'border-red-500' : ''}
                                            />
                                            {errors.website && (
                                                <p className="text-red-500 text-sm mt-1">{errors.website[0]}</p>
                                            )}
                                        </div>

                                        <div>
                                            <Label htmlFor="address">Address</Label>
                                            <Textarea
                                                id="address"
                                                name="address"
                                                value={formData.address}
                                                onChange={handleInputChange}
                                                placeholder="Physical address or meeting location"
                                                rows={2}
                                                className={errors.address ? 'border-red-500' : ''}
                                            />
                                            {errors.address && (
                                                <p className="text-red-500 text-sm mt-1">{errors.address[0]}</p>
                                            )}
                                        </div>
                                    </CardContent>
                                </Card>

                                {/* Media */}
                                <Card>
                                    <CardHeader>
                                        <CardTitle>Media</CardTitle>
                                        <CardDescription>
                                            Upload images to represent your organization
                                        </CardDescription>
                                    </CardHeader>
                                    <CardContent className="space-y-4">
                                        <div>
                                            <Label htmlFor="logo">Logo</Label>
                                            <div className="mt-1">
                                                <input
                                                    id="logo"
                                                    name="logo"
                                                    type="file"
                                                    accept="image/*"
                                                    onChange={handleFileChange}
                                                    className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                                                />
                                                <p className="text-xs text-gray-500 mt-1">PNG, JPG, GIF up to 2MB</p>
                                            </div>
                                            {errors.logo && (
                                                <p className="text-red-500 text-sm mt-1">{errors.logo[0]}</p>
                                            )}
                                        </div>

                                        <div>
                                            <Label htmlFor="banner_image">Banner Image</Label>
                                            <div className="mt-1">
                                                <input
                                                    id="banner_image"
                                                    name="banner_image"
                                                    type="file"
                                                    accept="image/*"
                                                    onChange={handleFileChange}
                                                    className="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100"
                                                />
                                                <p className="text-xs text-gray-500 mt-1">PNG, JPG, GIF up to 5MB</p>
                                            </div>
                                            {errors.banner_image && (
                                                <p className="text-red-500 text-sm mt-1">{errors.banner_image[0]}</p>
                                            )}
                                        </div>
                                    </CardContent>
                                </Card>

                                {/* Submit Button */}
                                <div className="flex justify-end space-x-4">
                                    <Button
                                        type="button"
                                        variant="outline"
                                        onClick={() => router.visit('/organizations')}
                                    >
                                        Cancel
                                    </Button>
                                    <Button type="submit" disabled={loading}>
                                        {loading ? 'Creating...' : 'Create Organization'}
                                    </Button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </AuthenticatedLayout>
    );
}
