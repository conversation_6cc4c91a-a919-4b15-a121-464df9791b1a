<?php

use App\Http\Controllers\DashboardController;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::get('/', function () {
    return Inertia::render('welcome');
})->name('home');

Route::middleware(['auth', 'verified'])->group(function () {
    Route::get('dashboard', [DashboardController::class, 'index'])->name('dashboard');

    // Organization routes
    Route::get('organizations', function () {
        return Inertia::render('organizations/index');
    })->name('organizations.index');

    Route::get('organizations/create', function () {
        return Inertia::render('organizations/create');
    })->name('organizations.create');

    Route::get('organizations/{organization}', function ($organization) {
        return Inertia::render('organizations/show', ['organizationId' => $organization]);
    })->name('organizations.show');

    // Post routes
    Route::get('posts', function () {
        return Inertia::render('posts/index');
    })->name('posts.index');

    Route::get('posts/create', function () {
        return Inertia::render('posts/create');
    })->name('posts.create');

    Route::get('posts/{post}', function ($post) {
        return Inertia::render('posts/show', ['postId' => $post]);
    })->name('posts.show');

    // Course routes
    Route::get('courses', function () {
        return Inertia::render('courses/index');
    })->name('courses.index');

    Route::get('courses/{course}', function ($course) {
        return Inertia::render('courses/show', ['courseId' => $course]);
    })->name('courses.show');

    // Profile routes
    Route::get('profile/{user}', function ($user) {
        return Inertia::render('profile/show', ['userId' => $user]);
    })->name('profile.show');
});

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
