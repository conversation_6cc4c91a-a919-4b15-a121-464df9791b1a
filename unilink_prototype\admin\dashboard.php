<?php
// Include admin authentication check
require_once '../includes/admin_check.php';

// Database connection
$conn = mysqli_connect("localhost", "root", "", "unilink_db");

// Check connection
if (!$conn) {
    die("Connection failed: " . mysqli_connect_error());
}

// Handle student validation
if (isset($_POST['validate_student'])) {
    $student_id = mysqli_real_escape_string($conn, $_POST['student_id']);
    $admin_id = $_SESSION['user_id'];
    
    $update_query = "UPDATE student_records SET 
                    is_validated = TRUE, 
                    validated_by = '$admin_id', 
                    validated_at = NOW() 
                    WHERE student_id = '$student_id'";
    
    mysqli_query($conn, $update_query);
}

// Get all student records
$query = "SELECT sr.*, u.id as user_id, u.username, u.email, u.created_at as registered_at, 
          admin.full_name as validated_by_name
          FROM student_records sr
          LEFT JOIN users u ON sr.student_id = u.username
          LEFT JOIN users admin ON sr.validated_by = admin.id
          ORDER BY sr.full_name";
          
$result = mysqli_query($conn, $query);
$students = mysqli_fetch_all($result, MYSQLI_ASSOC);

mysqli_close($conn);
?>

<!DOCTYPE html>
<html lang="en" data-bs-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Dashboard - UniLink</title>
    <!-- Bootstrap 5 CSS -->
    <link rel="stylesheet" href="../assets/libraries/css/bootstrap.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="../assets/libraries/fontawesome/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body>
    <!-- Include header/navbar here -->
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
        <div class="container">
            <a class="navbar-brand fw-bold" href="../index.php">
                <span style="color: #7BC74D;">Uni</span><span style="color: #EEEEEE;">Link</span>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.php"><i class="fas fa-home"></i> Home</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle active" href="#" id="adminDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user-shield"></i> Admin
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="adminDropdown">
                            <li><a class="dropdown-item" href="dashboard.php"><i class="fas fa-tachometer-alt me-2"></i>Dashboard</a></li>
                            <li><a class="dropdown-item" href="students.php"><i class="fas fa-user-graduate me-2"></i>Student Records</a></li>
                            <li><a class="dropdown-item active" href="validation.php"><i class="fas fa-user-check me-2"></i>Validate Students</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="settings.php"><i class="fas fa-cog me-2"></i>Settings</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    
    <div class="container mt-4">
        <div class="row">
            <div class="col-12">
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0"><i class="fas fa-users-cog me-2"></i>Student Validation</h4>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Student ID</th>
                                        <th>Full Name</th>
                                        <th>Registration Status</th>
                                        <th>Validation Status</th>
                                        <th>Validated By</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($students as $student): ?>
                                    <tr>
                                        <td><?php echo $student['student_id']; ?></td>
                                        <td><?php echo $student['full_name']; ?></td>
                                        <td>
                                            <?php if (isset($student['user_id'])): ?>
                                                <span class="badge bg-success">Registered</span>
                                                <small class="d-block text-muted">
                                                    <?php echo date('M d, Y', strtotime($student['registered_at'])); ?>
                                                </small>
                                            <?php else: ?>
                                                <span class="badge bg-warning">Not Registered</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if ($student['is_validated']): ?>
                                                <span class="badge bg-success">Validated</span>
                                                <small class="d-block text-muted">
                                                    <?php echo date('M d, Y', strtotime($student['validated_at'])); ?>
                                                </small>
                                            <?php else: ?>
                                                <span class="badge bg-danger">Not Validated</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php echo $student['validated_by_name'] ?? 'N/A'; ?>
                                        </td>
                                        <td>
                                            <?php if (!$student['is_validated']): ?>
                                            <form method="POST">
                                                <input type="hidden" name="student_id" value="<?php echo $student['student_id']; ?>">
                                                <button type="submit" name="validate_student" class="btn btn-sm btn-primary">
                                                    <i class="fas fa-user-check me-1"></i>Validate
                                                </button>
                                            </form>
                                            <?php else: ?>
                                                <button class="btn btn-sm btn-secondary" disabled>
                                                    <i class="fas fa-check-circle me-1"></i>Validated
                                                </button>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <br>
        <div class="card mb-4">
            <div class="card-header">
                <h5>Database Maintenance</h5>
            </div>
            <div class="card-body">
                <a href="../organizations/update_organizations_table.php" class="btn btn-primary">
                    Update Organizations Table
                </a>
            </div>
        </div>
        
        <!-- Add Organization Management section to admin dashboard -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>Organization Management</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <a href="../organization.php" class="btn btn-primary mb-2 w-100">
                            <i class="fas fa-building me-2"></i>View All Organizations
                        </a>
                    </div>
                    <div class="col-md-6">
                        <a href="manage_org_posts.php" class="btn btn-primary mb-2 w-100">
                            <i class="fas fa-clipboard-list me-2"></i>Manage Organization Posts
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Update organization links in the admin dashboard -->
        <div class="col-md-6 col-lg-4 mb-4">
            <div class="card h-100">
                <div class="card-body">
                    <h5 class="card-title">
                        <i class="fas fa-users text-primary"></i> Organizations
                    </h5>
                    <p class="card-text">Manage university organizations, departments, and clubs.</p>
                    <a href="../organizations/organization.php" class="btn btn-outline-primary">
                        <i class="fas fa-external-link-alt"></i> Go to Organizations
                    </a>
                </div>
            </div>
        </div>
        
        <!-- Add My Posts section to admin dashboard -->
        <div class="card mb-4">
            <div class="card-header">
                <h5>My Posts</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <a href="admin_posts.php" class="btn btn-primary mb-2 w-100">
                            <i class="fas fa-clipboard-list me-2"></i>View My Posts
                        </a>
                    </div>
                    <div class="col-md-6">
                        <a href="../create_post.php" class="btn btn-primary mb-2 w-100">
                            <i class="fas fa-plus-circle me-2"></i>Create New Post
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    
    
    <!-- Bootstrap JS Bundle with Popper -->
    <script src="../assets/libraries/js/bootstrap.bundle.min.js"></script>
    <!-- Include the dark mode script -->
    <script src="../assets/js/main.js"></script>
</body>
</html>
