<?php

use App\Models\User;
use App\Models\Post;
use App\Models\Organization;
use App\Models\OrganizationMember;

test('user can create post', function () {
    $user = User::factory()->create(['role' => 'student']);

    $postData = [
        'title' => 'Test Post',
        'content' => 'This is a test post content.',
        'type' => 'discussion',
        'visibility' => 'public',
        'comments_enabled' => true,
    ];

    $response = $this->actingAs($user, 'web')
                    ->postJson('/api/v1/posts', $postData);

    $response->assertStatus(201)
            ->assertJsonStructure([
                'message',
                'post' => [
                    'id',
                    'title',
                    'content',
                    'type',
                    'visibility',
                    'user_id',
                ]
            ]);

    $this->assertDatabaseHas('posts', [
        'title' => 'Test Post',
        'user_id' => $user->id,
        'visibility' => 'public',
    ]);
});

test('user can create organization post', function () {
    $user = User::factory()->create(['role' => 'student']);
    $organization = Organization::factory()->create(['status' => 'active']);

    // Make user a member of the organization
    OrganizationMember::create([
        'user_id' => $user->id,
        'organization_id' => $organization->id,
        'role' => 'member',
        'status' => 'active',
    ]);

    $postData = [
        'title' => 'Organization Post',
        'content' => 'This is an organization post.',
        'type' => 'announcement',
        'visibility' => 'members_only',
        'organization_id' => $organization->id,
        'comments_enabled' => true,
    ];

    $response = $this->actingAs($user, 'web')
                    ->postJson('/api/v1/posts', $postData);

    $response->assertStatus(201);

    $this->assertDatabaseHas('posts', [
        'title' => 'Organization Post',
        'user_id' => $user->id,
        'organization_id' => $organization->id,
        'visibility' => 'members_only',
    ]);
});

test('non member cannot create organization post', function () {
    $user = User::factory()->create(['role' => 'student']);
    $organization = Organization::factory()->create(['status' => 'active']);

    $postData = [
        'title' => 'Organization Post',
        'content' => 'This is an organization post.',
        'type' => 'announcement',
        'visibility' => 'members_only',
        'organization_id' => $organization->id,
        'comments_enabled' => true,
    ];

    $response = $this->actingAs($user, 'web')
                    ->postJson('/api/v1/posts', $postData);

    $response->assertStatus(403);
});

test('user can view public post', function () {
    $author = User::factory()->create(['role' => 'student']);
    $viewer = User::factory()->create(['role' => 'student']);

    $post = Post::factory()->create([
        'user_id' => $author->id,
        'visibility' => 'public',
        'published_at' => now(),
    ]);

    $response = $this->actingAs($viewer, 'web')
                    ->getJson("/api/v1/posts/{$post->id}");

    $response->assertStatus(200)
            ->assertJsonStructure([
                'id',
                'title',
                'content',
                'visibility',
                'user',
            ]);
});

test('non member cannot view members only post', function () {
    $author = User::factory()->create(['role' => 'student']);
    $viewer = User::factory()->create(['role' => 'student']);
    $organization = Organization::factory()->create(['status' => 'active']);

    // Make author a member but not viewer
    OrganizationMember::create([
        'user_id' => $author->id,
        'organization_id' => $organization->id,
        'role' => 'member',
        'status' => 'active',
    ]);

    $post = Post::factory()->create([
        'user_id' => $author->id,
        'organization_id' => $organization->id,
        'visibility' => 'members_only',
        'published_at' => now(),
    ]);

    $response = $this->actingAs($viewer, 'web')
                    ->getJson("/api/v1/posts/{$post->id}");

    $response->assertStatus(404);
});

test('post author can update their post', function () {
    $user = User::factory()->create(['role' => 'student']);

    $post = Post::factory()->create([
        'user_id' => $user->id,
        'title' => 'Original Title',
        'published_at' => now(),
    ]);

    $updateData = [
        'title' => 'Updated Title',
        'content' => 'Updated content',
        'type' => 'discussion',
        'visibility' => 'public',
        'comments_enabled' => true,
    ];

    $response = $this->actingAs($user, 'web')
                    ->putJson("/api/v1/posts/{$post->id}", $updateData);

    $response->assertStatus(200);

    $this->assertDatabaseHas('posts', [
        'id' => $post->id,
        'title' => 'Updated Title',
        'user_id' => $user->id,
    ]);
});

test('non author cannot update post', function () {
    $author = User::factory()->create(['role' => 'student']);
    $other = User::factory()->create(['role' => 'student']);

    $post = Post::factory()->create([
        'user_id' => $author->id,
        'published_at' => now(),
    ]);

    $updateData = [
        'title' => 'Hacked Title',
        'content' => 'Hacked content',
        'type' => 'discussion',
        'visibility' => 'public',
        'comments_enabled' => true,
    ];

    $response = $this->actingAs($other, 'web')
                    ->putJson("/api/v1/posts/{$post->id}", $updateData);

    $response->assertStatus(403);
});
