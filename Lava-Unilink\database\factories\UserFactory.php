<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\User>
 */
class UserFactory extends Factory
{
    /**
     * The current password being used by the factory.
     */
    protected static ?string $password;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $roles = ['student', 'faculty', 'admin'];
        $departments = ['Computer Science', 'Business Administration', 'Engineering', 'Mathematics', 'Physics'];
        $yearLevels = ['Freshman', 'Sophomore', 'Junior', 'Senior'];
        $campuses = ['Main Campus', 'North Campus', 'South Campus'];

        return [
            'name' => $this->faker->name(),
            'email' => $this->faker->unique()->safeEmail(),
            'email_verified_at' => now(),
            'password' => static::$password ??= Hash::make('password'),
            'remember_token' => Str::random(10),
            'student_id' => $this->faker->optional(0.8)->regexify('[A-Z]{3}[0-9]{3}'),
            'phone' => $this->faker->optional()->phoneNumber(),
            'birth_date' => $this->faker->optional()->dateTimeBetween('-30 years', '-18 years'),
            'bio' => $this->faker->optional()->paragraph(2),
            'campus' => $this->faker->randomElement($campuses),
            'department' => $this->faker->randomElement($departments),
            'year_level' => $this->faker->randomElement($yearLevels),
            'role' => $this->faker->randomElement($roles),
            'is_active' => true,
        ];
    }

    /**
     * Indicate that the model's email address should be unverified.
     */
    public function unverified(): static
    {
        return $this->state(fn (array $attributes) => [
            'email_verified_at' => null,
        ]);
    }

    /**
     * Indicate that the user is a student.
     */
    public function student(): static
    {
        return $this->state(fn (array $attributes) => [
            'role' => 'student',
            'student_id' => $this->faker->regexify('[A-Z]{3}[0-9]{3}'),
        ]);
    }

    /**
     * Indicate that the user is faculty.
     */
    public function faculty(): static
    {
        return $this->state(fn (array $attributes) => [
            'role' => 'faculty',
            'student_id' => null,
        ]);
    }

    /**
     * Indicate that the user is an admin.
     */
    public function admin(): static
    {
        return $this->state(fn (array $attributes) => [
            'role' => 'admin',
            'student_id' => null,
        ]);
    }
}
