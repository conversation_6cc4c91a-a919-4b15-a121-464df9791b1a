<?php

namespace App\Events;

use App\Models\Post;
use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class PostCreated implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $post;

    /**
     * Create a new event instance.
     */
    public function __construct(Post $post)
    {
        $this->post = $post;
    }

    /**
     * Get the channels the event should broadcast on.
     *
     * @return array<int, \Illuminate\Broadcasting\Channel>
     */
    public function broadcastOn(): array
    {
        $channels = [
            new Channel('posts'), // Public channel for all posts
        ];

        // Add organization-specific channel if post belongs to an organization
        if ($this->post->organization_id) {
            $channels[] = new PrivateChannel('organization.' . $this->post->organization_id);
        }

        return $channels;
    }

    /**
     * Get the data to broadcast.
     *
     * @return array
     */
    public function broadcastWith(): array
    {
        return [
            'post' => [
                'id' => $this->post->id,
                'title' => $this->post->title,
                'content' => $this->post->content,
                'type' => $this->post->type,
                'visibility' => $this->post->visibility,
                'user' => [
                    'id' => $this->post->user->id,
                    'name' => $this->post->user->name,
                    'avatar' => $this->post->user->avatar,
                ],
                'organization' => $this->post->organization ? [
                    'id' => $this->post->organization->id,
                    'name' => $this->post->organization->name,
                    'logo' => $this->post->organization->logo,
                ] : null,
                'created_at' => $this->post->created_at->toISOString(),
            ]
        ];
    }

    /**
     * The event's broadcast name.
     *
     * @return string
     */
    public function broadcastAs(): string
    {
        return 'post.created';
    }
}
