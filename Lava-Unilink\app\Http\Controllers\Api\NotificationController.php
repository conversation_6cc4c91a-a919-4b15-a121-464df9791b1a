<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class NotificationController extends Controller
{
    /**
     * Get user's notifications.
     */
    public function index(Request $request): JsonResponse
    {
        $user = Auth::user();

        $notifications = DB::table('notifications')
                          ->where('notifiable_type', get_class($user))
                          ->where('notifiable_id', $user->id)
                          ->orderBy('created_at', 'desc')
                          ->paginate($request->get('per_page', 20));

        return response()->json($notifications);
    }

    /**
     * Mark notification as read.
     */
    public function markAsRead(Request $request, $notificationId): JsonResponse
    {
        $user = Auth::user();

        $updated = DB::table('notifications')
                    ->where('id', $notificationId)
                    ->where('notifiable_type', get_class($user))
                    ->where('notifiable_id', $user->id)
                    ->whereNull('read_at')
                    ->update(['read_at' => now()]);

        if ($updated) {
            return response()->json(['message' => 'Notification marked as read']);
        }

        return response()->json(['message' => 'Notification not found'], 404);
    }

    /**
     * Mark all notifications as read.
     */
    public function markAllAsRead(): JsonResponse
    {
        $user = Auth::user();

        DB::table('notifications')
          ->where('notifiable_type', get_class($user))
          ->where('notifiable_id', $user->id)
          ->whereNull('read_at')
          ->update(['read_at' => now()]);

        return response()->json(['message' => 'All notifications marked as read']);
    }

    /**
     * Get unread notification count.
     */
    public function unreadCount(): JsonResponse
    {
        $user = Auth::user();

        $count = DB::table('notifications')
                  ->where('notifiable_type', get_class($user))
                  ->where('notifiable_id', $user->id)
                  ->whereNull('read_at')
                  ->count();

        return response()->json(['unread_count' => $count]);
    }

    /**
     * Delete a notification.
     */
    public function destroy($notificationId): JsonResponse
    {
        $user = Auth::user();

        $deleted = DB::table('notifications')
                    ->where('id', $notificationId)
                    ->where('notifiable_type', get_class($user))
                    ->where('notifiable_id', $user->id)
                    ->delete();

        if ($deleted) {
            return response()->json(['message' => 'Notification deleted']);
        }

        return response()->json(['message' => 'Notification not found'], 404);
    }
}
