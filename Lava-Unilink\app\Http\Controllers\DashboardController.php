<?php

namespace App\Http\Controllers;

use App\Models\Post;
use App\Models\Organization;
use App\Models\Course;
use App\Models\Comment;
use App\Models\Reaction;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use Inertia\Response;

class DashboardController extends Controller
{
    /**
     * Display the dashboard.
     */
    public function index(Request $request): Response
    {
        $user = Auth::user();

        // Get user's organizations
        $userOrganizations = $user->organizations()
                                 ->where('status', 'active')
                                 ->withCount(['posts', 'members'])
                                 ->get();

        // Get recent posts from user's organizations and public posts
        $organizationIds = $userOrganizations->pluck('id')->toArray();

        $recentPosts = Post::with(['user', 'organization', 'reactions'])
                          ->withCount(['comments', 'reactions'])
                          ->where(function ($query) use ($organizationIds, $user) {
                              $query->where('visibility', 'public')
                                    ->orWhere(function ($q) use ($organizationIds) {
                                        $q->where('visibility', 'members_only')
                                          ->whereIn('organization_id', $organizationIds);
                                    })
                                    ->orWhere('user_id', $user->id);
                          })
                          ->published()
                          ->orderBy('is_pinned', 'desc')
                          ->orderBy('created_at', 'desc')
                          ->take(10)
                          ->get();

        // Get user's enrolled courses
        $enrolledCourses = $user->courses()
                               ->where('status', 'enrolled')
                               ->withCount('enrolledStudents')
                               ->get();

        // Get recent activity stats
        $stats = [
            'total_posts' => $user->posts()->count(),
            'total_comments' => $user->comments()->count(),
            'total_reactions' => $user->reactions()->count(),
            'organizations_count' => $userOrganizations->count(),
            'courses_count' => $enrolledCourses->count(),
        ];

        // Get recent notifications (if implemented)
        $recentNotifications = collect(); // Placeholder for notifications

        // Get trending organizations
        $trendingOrganizations = Organization::where('status', 'active')
                                           ->withCount(['posts' => function ($query) {
                                               $query->where('created_at', '>=', now()->subWeek());
                                           }])
                                           ->orderBy('posts_count', 'desc')
                                           ->take(5)
                                           ->get();

        return Inertia::render('dashboard', [
            'user' => $user,
            'userOrganizations' => $userOrganizations,
            'recentPosts' => $recentPosts,
            'enrolledCourses' => $enrolledCourses,
            'stats' => $stats,
            'recentNotifications' => $recentNotifications,
            'trendingOrganizations' => $trendingOrganizations,
        ]);
    }

    /**
     * Get dashboard data as JSON (for API calls).
     */
    public function data(Request $request)
    {
        $user = Auth::user();

        // Get user's organizations
        $userOrganizations = $user->organizations()
                                 ->where('status', 'active')
                                 ->withCount(['posts', 'members'])
                                 ->get();

        // Get recent posts
        $organizationIds = $userOrganizations->pluck('id')->toArray();

        $recentPosts = Post::with(['user', 'organization', 'reactions'])
                          ->withCount(['comments', 'reactions'])
                          ->where(function ($query) use ($organizationIds, $user) {
                              $query->where('visibility', 'public')
                                    ->orWhere(function ($q) use ($organizationIds) {
                                        $q->where('visibility', 'members_only')
                                          ->whereIn('organization_id', $organizationIds);
                                    })
                                    ->orWhere('user_id', $user->id);
                          })
                          ->published()
                          ->orderBy('is_pinned', 'desc')
                          ->orderBy('created_at', 'desc')
                          ->paginate($request->get('per_page', 10));

        return response()->json([
            'posts' => $recentPosts,
            'organizations' => $userOrganizations,
        ]);
    }

    /**
     * Get user statistics.
     */
    public function stats()
    {
        $user = Auth::user();

        $stats = [
            'posts' => [
                'total' => $user->posts()->count(),
                'this_month' => $user->posts()->where('created_at', '>=', now()->startOfMonth())->count(),
                'published' => $user->posts()->published()->count(),
            ],
            'comments' => [
                'total' => $user->comments()->count(),
                'this_month' => $user->comments()->where('created_at', '>=', now()->startOfMonth())->count(),
            ],
            'reactions' => [
                'given' => $user->reactions()->count(),
                'received' => Reaction::whereHasMorph('reactable', [Post::class, Comment::class], function ($query) use ($user) {
                    $query->where('user_id', $user->id);
                })->count(),
            ],
            'organizations' => [
                'member_of' => $user->organizations()->where('status', 'active')->count(),
                'created' => $user->createdOrganizations()->count(),
                'admin_of' => $user->organizations()->wherePivot('role', 'admin')->count(),
            ],
            'courses' => [
                'enrolled' => $user->courses()->where('status', 'enrolled')->count(),
                'completed' => $user->courses()->where('status', 'completed')->count(),
            ],
        ];

        return response()->json($stats);
    }

    /**
     * Get activity feed for the user.
     */
    public function activity(Request $request)
    {
        $user = Auth::user();
        $organizationIds = $user->organizations()->where('status', 'active')->pluck('id')->toArray();

        // Get posts from user's organizations and public posts
        $posts = Post::with(['user', 'organization', 'reactions'])
                    ->withCount(['comments', 'reactions'])
                    ->where(function ($query) use ($organizationIds, $user) {
                        $query->where('visibility', 'public')
                              ->orWhere(function ($q) use ($organizationIds) {
                                  $q->where('visibility', 'members_only')
                                    ->whereIn('organization_id', $organizationIds);
                              })
                              ->orWhere('user_id', $user->id);
                    })
                    ->published()
                    ->orderBy('is_pinned', 'desc')
                    ->orderBy('created_at', 'desc')
                    ->paginate($request->get('per_page', 15));

        return response()->json($posts);
    }
}
