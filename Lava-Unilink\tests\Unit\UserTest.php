<?php

use App\Models\User;
use App\Models\Organization;
use App\Models\OrganizationMember;
use App\Models\Course;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

test('user can check if they are a student', function () {
    $user = new User(['role' => 'student']);

    expect($user->isStudent())->toBeTrue();
    expect($user->isFaculty())->toBeFalse();
    expect($user->isAdmin())->toBeFalse();
});

test('user can check if they are faculty', function () {
    $user = new User(['role' => 'faculty']);

    expect($user->isFaculty())->toBeTrue();
    expect($user->isStudent())->toBeFalse();
    expect($user->isAdmin())->toBeFalse();
});

test('user can check if they are admin', function () {
    $user = new User(['role' => 'admin']);

    expect($user->isAdmin())->toBeTrue();
    expect($user->isStudent())->toBeFalse();
    expect($user->isFaculty())->toBeFalse();
});

test('user can check organization membership', function () {
    $user = User::factory()->create(['role' => 'student']);
    $organization = Organization::factory()->create(['status' => 'active']);

    // Initially not a member
    expect($user->isMemberOf($organization))->toBeFalse();

    // Add membership
    OrganizationMember::create([
        'user_id' => $user->id,
        'organization_id' => $organization->id,
        'role' => 'member',
        'status' => 'active',
    ]);

    // Refresh the user model to get updated relationships
    $user->refresh();

    expect($user->isMemberOf($organization))->toBeTrue();
});

test('user can check organization admin status', function () {
    $user = User::factory()->create(['role' => 'student']);
    $organization = Organization::factory()->create(['status' => 'active']);

    // Initially not an admin
    expect($user->isAdminOf($organization))->toBeFalse();

    // Add admin membership
    OrganizationMember::create([
        'user_id' => $user->id,
        'organization_id' => $organization->id,
        'role' => 'admin',
        'status' => 'active',
    ]);

    // Refresh the user model
    $user->refresh();

    expect($user->isAdminOf($organization))->toBeTrue();
});

test('user can have multiple organization memberships', function () {
    $user = User::factory()->create(['role' => 'student']);
    $org1 = Organization::factory()->create(['status' => 'active']);
    $org2 = Organization::factory()->create(['status' => 'active']);

    // Add memberships
    OrganizationMember::create([
        'user_id' => $user->id,
        'organization_id' => $org1->id,
        'role' => 'admin',
        'status' => 'active',
    ]);

    OrganizationMember::create([
        'user_id' => $user->id,
        'organization_id' => $org2->id,
        'role' => 'member',
        'status' => 'active',
    ]);

    // Refresh the user model
    $user->refresh();

    expect($user->organizations)->toHaveCount(2);
    expect($user->isAdminOf($org1))->toBeTrue();
    expect($user->isAdminOf($org2))->toBeFalse();
    expect($user->isMemberOf($org1))->toBeTrue();
    expect($user->isMemberOf($org2))->toBeTrue();
});

test('user can be enrolled in courses', function () {
    $user = User::factory()->create(['role' => 'student']);
    $course = Course::factory()->create();

    // Enroll user in course
    $user->courses()->attach($course->id, [
        'status' => 'enrolled',
        'created_at' => now(),
        'updated_at' => now(),
    ]);

    // Refresh the user model
    $user->refresh();

    expect($user->courses)->toHaveCount(1);
    expect($user->courses->first()->id)->toBe($course->id);
});
