<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Comment;
use App\Models\Post;
use App\Events\CommentCreated;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class CommentController extends Controller
{
    /**
     * Display comments for a specific post.
     */
    public function index(Request $request, Post $post): JsonResponse
    {
        // Check if user can view this post
        if ($post->visibility === 'private' && $post->user_id !== Auth::id() && !Auth::user()->isAdmin()) {
            return response()->json(['message' => 'Post not found'], 404);
        }

        if ($post->visibility === 'members_only' && $post->organization_id) {
            $organization = $post->organization;
            if (!$organization->hasMember(Auth::user()) && !Auth::user()->isAdmin()) {
                return response()->json(['message' => 'Post not found'], 404);
            }
        }

        $comments = $post->comments()
                        ->topLevel()
                        ->with([
                            'user',
                            'replies' => function ($query) {
                                $query->with(['user', 'reactions'])->latest();
                            },
                            'reactions'
                        ])
                        ->withCount(['replies', 'reactions'])
                        ->latest()
                        ->paginate($request->get('per_page', 20));

        return response()->json($comments);
    }

    /**
     * Store a newly created comment.
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'content' => 'required|string|max:1000',
            'post_id' => 'required|exists:posts,id',
            'parent_id' => 'nullable|exists:comments,id',
        ]);

        $post = Post::find($validated['post_id']);

        // Check if comments are enabled for this post
        if (!$post->comments_enabled) {
            return response()->json(['message' => 'Comments are disabled for this post'], 403);
        }

        // Check if user can comment on this post
        if ($post->visibility === 'private' && $post->user_id !== Auth::id() && !Auth::user()->isAdmin()) {
            return response()->json(['message' => 'You cannot comment on this post'], 403);
        }

        if ($post->visibility === 'members_only' && $post->organization_id) {
            $organization = $post->organization;
            if (!$organization->hasMember(Auth::user()) && !Auth::user()->isAdmin()) {
                return response()->json(['message' => 'You cannot comment on this post'], 403);
            }
        }

        // If replying to a comment, ensure the parent comment belongs to the same post
        if ($validated['parent_id']) {
            $parentComment = Comment::find($validated['parent_id']);
            if ($parentComment->post_id !== $validated['post_id']) {
                return response()->json(['message' => 'Invalid parent comment'], 400);
            }
        }

        $validated['user_id'] = Auth::id();

        $comment = Comment::create($validated);
        $comment->load(['user', 'reactions']);

        // Fire the CommentCreated event for real-time updates
        event(new CommentCreated($comment));

        return response()->json([
            'message' => 'Comment created successfully',
            'comment' => $comment
        ], 201);
    }

    /**
     * Display the specified comment.
     */
    public function show(Comment $comment): JsonResponse
    {
        $comment->load([
            'user',
            'post',
            'parent',
            'replies' => function ($query) {
                $query->with(['user', 'reactions'])->latest();
            },
            'reactions'
        ]);

        $comment->loadCount(['replies', 'reactions']);

        return response()->json($comment);
    }

    /**
     * Update the specified comment.
     */
    public function update(Request $request, Comment $comment): JsonResponse
    {
        // Check if user can update this comment
        if ($comment->user_id !== Auth::id() && !Auth::user()->isAdmin()) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $validated = $request->validate([
            'content' => 'required|string|max:1000',
        ]);

        $comment->update([
            'content' => $validated['content'],
            'is_edited' => true,
            'edited_at' => now(),
        ]);

        return response()->json([
            'message' => 'Comment updated successfully',
            'comment' => $comment->load(['user', 'reactions'])
        ]);
    }

    /**
     * Remove the specified comment.
     */
    public function destroy(Comment $comment): JsonResponse
    {
        // Check if user can delete this comment
        if ($comment->user_id !== Auth::id() && !Auth::user()->isAdmin()) {
            // Check if user is admin of the organization (if post belongs to organization)
            if ($comment->post->organization_id) {
                $organization = $comment->post->organization;
                if (!$organization->hasAdmin(Auth::user())) {
                    return response()->json(['message' => 'Unauthorized'], 403);
                }
            } else {
                return response()->json(['message' => 'Unauthorized'], 403);
            }
        }

        $comment->delete();

        return response()->json(['message' => 'Comment deleted successfully']);
    }

    /**
     * Get replies for a specific comment.
     */
    public function replies(Request $request, Comment $comment): JsonResponse
    {
        $replies = $comment->replies()
                          ->with(['user', 'reactions'])
                          ->withCount(['reactions'])
                          ->latest()
                          ->paginate($request->get('per_page', 10));

        return response()->json($replies);
    }
}
