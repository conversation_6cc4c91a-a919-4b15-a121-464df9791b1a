<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Course>
 */
class CourseFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $departments = ['Computer Science', 'Business Administration', 'Engineering', 'Mathematics', 'Physics', 'Chemistry', 'Biology', 'Psychology', 'English', 'History'];
        $prefixes = ['CS', 'BUS', 'ENG', 'MATH', 'PHYS', 'CHEM', 'BIO', 'PSY', 'ENG', 'HIST'];
        $semesters = ['Fall 2024', 'Spring 2025', 'Summer 2024', 'Fall 2025'];

        $department = fake()->randomElement($departments);
        $prefix = $prefixes[array_search($department, $departments)];

        return [
            'code' => $prefix . fake()->numberBetween(100, 599),
            'name' => fake()->sentence(3, true),
            'description' => fake()->paragraph(2),
            'department' => $department,
            'credits' => fake()->numberBetween(1, 4),
            'semester' => fake()->randomElement($semesters),
            'instructor' => 'Dr. ' . fake()->lastName(),
            'capacity' => fake()->optional(0.8)->numberBetween(15, 50),
        ];
    }

    /**
     * Indicate that the course is a computer science course.
     */
    public function computerScience(): static
    {
        return $this->state(fn (array $attributes) => [
            'department' => 'Computer Science',
            'code' => 'CS' . fake()->numberBetween(100, 599),
        ]);
    }

    /**
     * Indicate that the course is a business course.
     */
    public function business(): static
    {
        return $this->state(fn (array $attributes) => [
            'department' => 'Business Administration',
            'code' => 'BUS' . fake()->numberBetween(100, 599),
        ]);
    }

    /**
     * Indicate that the course has no capacity limit.
     */
    public function unlimited(): static
    {
        return $this->state(fn (array $attributes) => [
            'capacity' => null,
        ]);
    }

    /**
     * Indicate that the course is full capacity.
     */
    public function fullCapacity(): static
    {
        return $this->state(fn (array $attributes) => [
            'capacity' => 20,
        ]);
    }
}
