<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Organization;
use App\Models\OrganizationMember;
use App\Models\Course;
use App\Models\Post;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create admin user
        $admin = User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'admin',
            'student_id' => 'ADM001',
            'campus' => 'Main Campus',
            'department' => 'Administration',
            'is_active' => true,
            'email_verified_at' => now(),
        ]);

        // Create faculty user
        $faculty = User::create([
            'name' => 'Dr. <PERSON>',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'faculty',
            'campus' => 'Main Campus',
            'department' => 'Computer Science',
            'is_active' => true,
            'email_verified_at' => now(),
        ]);

        // Create student users
        $student1 = User::create([
            'name' => 'John Doe',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'student',
            'student_id' => 'STU001',
            'campus' => 'Main Campus',
            'department' => 'Computer Science',
            'year_level' => 'Junior',
            'bio' => 'Computer Science student passionate about web development.',
            'is_active' => true,
            'email_verified_at' => now(),
        ]);

        $student2 = User::create([
            'name' => 'Alice Johnson',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'student',
            'student_id' => 'STU002',
            'campus' => 'Main Campus',
            'department' => 'Business Administration',
            'year_level' => 'Senior',
            'bio' => 'Business student interested in entrepreneurship.',
            'is_active' => true,
            'email_verified_at' => now(),
        ]);

        // Create sample organizations
        $csClub = Organization::create([
            'name' => 'Computer Science Club',
            'description' => 'A club for computer science students to collaborate and learn together.',
            'type' => 'club',
            'email' => '<EMAIL>',
            'campus' => 'Main Campus',
            'status' => 'active',
            'created_by' => $student1->id,
        ]);

        $businessSociety = Organization::create([
            'name' => 'Business Society',
            'description' => 'Connecting business students with industry professionals.',
            'type' => 'society',
            'email' => '<EMAIL>',
            'campus' => 'Main Campus',
            'status' => 'active',
            'created_by' => $student2->id,
        ]);

        // Create organization memberships
        OrganizationMember::create([
            'user_id' => $student1->id,
            'organization_id' => $csClub->id,
            'role' => 'admin',
            'status' => 'active',
            'joined_at' => now(),
        ]);

        OrganizationMember::create([
            'user_id' => $student2->id,
            'organization_id' => $businessSociety->id,
            'role' => 'admin',
            'status' => 'active',
            'joined_at' => now(),
        ]);

        // Cross-membership
        OrganizationMember::create([
            'user_id' => $student2->id,
            'organization_id' => $csClub->id,
            'role' => 'member',
            'status' => 'active',
            'joined_at' => now(),
        ]);

        // Create sample courses
        $course1 = Course::create([
            'code' => 'CS101',
            'name' => 'Introduction to Computer Science',
            'description' => 'Basic concepts of computer science and programming.',
            'department' => 'Computer Science',
            'credits' => 3,
            'semester' => 'Fall 2024',
            'instructor' => 'Dr. Jane Smith',
            'capacity' => 30,
        ]);

        $course2 = Course::create([
            'code' => 'BUS201',
            'name' => 'Business Management',
            'description' => 'Fundamentals of business management and leadership.',
            'department' => 'Business Administration',
            'credits' => 3,
            'semester' => 'Fall 2024',
            'instructor' => 'Prof. Robert Brown',
            'capacity' => 25,
        ]);

        // Enroll students in courses
        $student1->courses()->attach($course1->id, ['status' => 'enrolled']);
        $student2->courses()->attach($course2->id, ['status' => 'enrolled']);

        // Create sample posts
        Post::create([
            'title' => 'Welcome to Computer Science Club!',
            'content' => 'We are excited to welcome all new and returning members to the Computer Science Club. Join us for our first meeting next week!',
            'type' => 'announcement',
            'visibility' => 'public',
            'user_id' => $student1->id,
            'organization_id' => $csClub->id,
            'published_at' => now(),
        ]);

        Post::create([
            'title' => 'Upcoming Hackathon Event',
            'content' => 'Get ready for our annual hackathon! Teams of 3-4 students will compete to build innovative solutions. Prizes include internship opportunities and cash rewards.',
            'type' => 'event',
            'visibility' => 'public',
            'user_id' => $student1->id,
            'organization_id' => $csClub->id,
            'published_at' => now(),
        ]);

        Post::create([
            'title' => 'Business Networking Event',
            'content' => 'Join us for an exclusive networking event with industry leaders. This is a great opportunity to make connections and learn about career opportunities.',
            'type' => 'event',
            'visibility' => 'members_only',
            'user_id' => $student2->id,
            'organization_id' => $businessSociety->id,
            'published_at' => now(),
        ]);
    }
}
