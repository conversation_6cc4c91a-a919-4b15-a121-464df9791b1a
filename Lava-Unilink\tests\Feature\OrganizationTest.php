<?php

use App\Models\User;
use App\Models\Organization;
use App\Models\OrganizationMember;

test('user can create organization', function () {
    $user = User::factory()->create(['role' => 'student']);

    $organizationData = [
        'name' => 'Test Organization',
        'description' => 'A test organization',
        'type' => 'club',
        'email' => '<EMAIL>',
        'campus' => 'Main Campus',
    ];

    $response = $this->actingAs($user, 'web')
                    ->postJson('/api/v1/organizations', $organizationData);

    $response->assertStatus(201)
            ->assertJsonStructure([
                'message',
                'organization' => [
                    'id',
                    'name',
                    'description',
                    'type',
                    'email',
                    'campus',
                    'status',
                    'created_by',
                ]
            ]);

    $this->assertDatabaseHas('organizations', [
        'name' => 'Test Organization',
        'created_by' => $user->id,
        'status' => 'pending',
    ]);

    // Check that creator is automatically made an admin member
    $this->assertDatabaseHas('organization_members', [
        'user_id' => $user->id,
        'organization_id' => Organization::where('name', 'Test Organization')->first()->id,
        'role' => 'admin',
        'status' => 'active',
    ]);
});

test('user can join organization', function () {
    $creator = User::factory()->create(['role' => 'student']);
    $joiner = User::factory()->create(['role' => 'student']);

    $organization = Organization::factory()->create([
        'created_by' => $creator->id,
        'status' => 'active',
    ]);

    // Creator membership
    OrganizationMember::create([
        'user_id' => $creator->id,
        'organization_id' => $organization->id,
        'role' => 'admin',
        'status' => 'active',
    ]);

    $response = $this->actingAs($joiner, 'web')
                    ->postJson("/api/v1/organizations/{$organization->id}/join");

    $response->assertStatus(200)
            ->assertJson(['message' => 'Membership request sent successfully']);

    $this->assertDatabaseHas('organization_members', [
        'user_id' => $joiner->id,
        'organization_id' => $organization->id,
        'role' => 'member',
        'status' => 'pending',
    ]);
});

test('organization admin can approve member', function () {
    $admin = User::factory()->create(['role' => 'student']);
    $member = User::factory()->create(['role' => 'student']);

    $organization = Organization::factory()->create([
        'created_by' => $admin->id,
        'status' => 'active',
    ]);

    // Admin membership
    OrganizationMember::create([
        'user_id' => $admin->id,
        'organization_id' => $organization->id,
        'role' => 'admin',
        'status' => 'active',
    ]);

    // Pending member
    OrganizationMember::create([
        'user_id' => $member->id,
        'organization_id' => $organization->id,
        'role' => 'member',
        'status' => 'pending',
    ]);

    $response = $this->actingAs($admin, 'web')
                    ->putJson("/api/v1/organizations/{$organization->id}/members/{$member->id}", [
                        'status' => 'active',
                    ]);

    $response->assertStatus(200);

    $this->assertDatabaseHas('organization_members', [
        'user_id' => $member->id,
        'organization_id' => $organization->id,
        'status' => 'active',
    ]);
});

test('non admin cannot approve members', function () {
    $member1 = User::factory()->create(['role' => 'student']);
    $member2 = User::factory()->create(['role' => 'student']);

    $organization = Organization::factory()->create(['status' => 'active']);

    // Regular member
    OrganizationMember::create([
        'user_id' => $member1->id,
        'organization_id' => $organization->id,
        'role' => 'member',
        'status' => 'active',
    ]);

    // Pending member
    OrganizationMember::create([
        'user_id' => $member2->id,
        'organization_id' => $organization->id,
        'role' => 'member',
        'status' => 'pending',
    ]);

    $response = $this->actingAs($member1, 'web')
                    ->putJson("/api/v1/organizations/{$organization->id}/members/{$member2->id}", [
                        'status' => 'active',
                    ]);

    $response->assertStatus(403);
});
