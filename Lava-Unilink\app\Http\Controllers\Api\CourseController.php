<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Course;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class CourseController extends Controller
{
    /**
     * Display a listing of courses.
     */
    public function index(Request $request): JsonResponse
    {
        $query = Course::withCount(['users', 'enrolledStudents']);

        // Filter by department
        if ($request->has('department')) {
            $query->where('department', $request->department);
        }

        // Filter by semester
        if ($request->has('semester')) {
            $query->where('semester', $request->semester);
        }

        // Filter by instructor
        if ($request->has('instructor')) {
            $query->where('instructor', 'like', "%{$request->instructor}%");
        }

        // Search by code or name
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('code', 'like', "%{$search}%")
                  ->orWhere('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        // Filter by availability (not at capacity)
        if ($request->has('available') && $request->available) {
            $query->whereRaw('capacity IS NULL OR capacity > (
                SELECT COUNT(*) FROM user_courses
                WHERE course_id = courses.id AND status = "enrolled"
            )');
        }

        $courses = $query->paginate($request->get('per_page', 15));

        return response()->json($courses);
    }

    /**
     * Store a newly created course.
     */
    public function store(Request $request): JsonResponse
    {
        // Only faculty and admin can create courses
        if (!Auth::user()->isFaculty() && !Auth::user()->isAdmin()) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $validated = $request->validate([
            'code' => 'required|string|max:20|unique:courses',
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'department' => 'required|string|max:255',
            'credits' => 'required|integer|min:1|max:6',
            'semester' => 'required|string|max:255',
            'instructor' => 'nullable|string|max:255',
            'capacity' => 'nullable|integer|min:1',
        ]);

        $course = Course::create($validated);

        return response()->json([
            'message' => 'Course created successfully',
            'course' => $course
        ], 201);
    }

    /**
     * Display the specified course.
     */
    public function show(Course $course): JsonResponse
    {
        $course->load(['enrolledStudents.user'])
               ->loadCount(['users', 'enrolledStudents']);

        return response()->json($course);
    }

    /**
     * Update the specified course.
     */
    public function update(Request $request, Course $course): JsonResponse
    {
        // Only faculty and admin can update courses
        if (!Auth::user()->isFaculty() && !Auth::user()->isAdmin()) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $validated = $request->validate([
            'code' => ['required', 'string', 'max:20', Rule::unique('courses')->ignore($course->id)],
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'department' => 'required|string|max:255',
            'credits' => 'required|integer|min:1|max:6',
            'semester' => 'required|string|max:255',
            'instructor' => 'nullable|string|max:255',
            'capacity' => 'nullable|integer|min:1',
        ]);

        $course->update($validated);

        return response()->json([
            'message' => 'Course updated successfully',
            'course' => $course
        ]);
    }

    /**
     * Remove the specified course.
     */
    public function destroy(Course $course): JsonResponse
    {
        // Only admin can delete courses
        if (!Auth::user()->isAdmin()) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $course->delete();

        return response()->json(['message' => 'Course deleted successfully']);
    }

    /**
     * Enroll current user in a course.
     */
    public function enroll(Course $course): JsonResponse
    {
        $user = Auth::user();

        // Check if already enrolled
        if ($course->users()->where('user_id', $user->id)->exists()) {
            return response()->json(['message' => 'Already enrolled in this course'], 400);
        }

        // Check capacity
        if ($course->isAtCapacity()) {
            return response()->json(['message' => 'Course is at capacity'], 400);
        }

        $course->users()->attach($user->id, [
            'status' => 'enrolled',
            'created_at' => now(),
            'updated_at' => now(),
        ]);

        return response()->json(['message' => 'Successfully enrolled in course']);
    }

    /**
     * Drop current user from a course.
     */
    public function drop(Course $course): JsonResponse
    {
        $user = Auth::user();

        $enrollment = $course->users()->where('user_id', $user->id)->first();

        if (!$enrollment) {
            return response()->json(['message' => 'Not enrolled in this course'], 400);
        }

        // Update status to dropped instead of removing the record
        $course->users()->updateExistingPivot($user->id, [
            'status' => 'dropped',
            'updated_at' => now(),
        ]);

        return response()->json(['message' => 'Successfully dropped from course']);
    }

    /**
     * Get enrolled students for a course.
     */
    public function students(Course $course): JsonResponse
    {
        // Only faculty and admin can view student list
        if (!Auth::user()->isFaculty() && !Auth::user()->isAdmin()) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $students = $course->enrolledStudents()
                          ->with('user')
                          ->withPivot(['status', 'grade', 'created_at'])
                          ->get();

        return response()->json($students);
    }
}
