<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Organization extends Model
{
    protected $fillable = [
        'name',
        'description',
        'type',
        'email',
        'phone',
        'website',
        'logo',
        'banner_image',
        'address',
        'campus',
        'status',
        'created_by',
    ];

    protected $casts = [
        'status' => 'string',
    ];

    /**
     * Get the user who created this organization.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get all posts for this organization.
     */
    public function posts(): <PERSON><PERSON><PERSON>
    {
        return $this->hasMany(Post::class);
    }

    /**
     * Get all members of this organization.
     */
    public function members(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'organization_members')
                    ->withPivot(['role', 'status', 'joined_at'])
                    ->withTimestamps();
    }

    /**
     * Get organization membership records.
     */
    public function memberships(): <PERSON><PERSON><PERSON>
    {
        return $this->hasMany(OrganizationMember::class);
    }

    /**
     * Check if a user is a member of this organization.
     */
    public function hasMember(User $user): bool
    {
        return $this->members()->where('user_id', $user->id)->exists();
    }

    /**
     * Check if a user is an admin of this organization.
     */
    public function hasAdmin(User $user): bool
    {
        return $this->members()
                    ->where('user_id', $user->id)
                    ->wherePivot('role', 'admin')
                    ->exists();
    }
}
