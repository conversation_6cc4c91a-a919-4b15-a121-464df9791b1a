<?php

use App\Http\Controllers\Api\OrganizationController;
use App\Http\Controllers\Api\PostController;
use App\Http\Controllers\Api\CommentController;
use App\Http\Controllers\Api\ReactionController;
use App\Http\Controllers\Api\CourseController;
use App\Http\Controllers\Api\NotificationController;
use App\Http\Controllers\DashboardController;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// Public routes (no authentication required)
Route::prefix('v1')->group(function () {
    // Organizations - public listing and viewing
    Route::get('organizations', [OrganizationController::class, 'index']);
    Route::get('organizations/{organization}', [OrganizationController::class, 'show']);
    
    // Posts - public listing and viewing
    Route::get('posts', [PostController::class, 'index']);
    Route::get('posts/{post}', [PostController::class, 'show']);
    
    // Comments - public viewing
    Route::get('posts/{post}/comments', [CommentController::class, 'index']);
    Route::get('comments/{comment}', [CommentController::class, 'show']);
    Route::get('comments/{comment}/replies', [CommentController::class, 'replies']);
    
    // Reactions - public viewing
    Route::get('reactions', [ReactionController::class, 'index']);
    Route::get('reactions/summary', [ReactionController::class, 'summary']);
    Route::get('reactions/types', [ReactionController::class, 'types']);
    
    // Courses - public listing and viewing
    Route::get('courses', [CourseController::class, 'index']);
    Route::get('courses/{course}', [CourseController::class, 'show']);
});

// Protected routes (authentication required)
Route::middleware(['auth:sanctum'])->prefix('v1')->group(function () {
    // Dashboard
    Route::get('dashboard/data', [DashboardController::class, 'data']);
    Route::get('dashboard/stats', [DashboardController::class, 'stats']);
    Route::get('dashboard/activity', [DashboardController::class, 'activity']);
    
    // Organizations - authenticated actions
    Route::post('organizations', [OrganizationController::class, 'store']);
    Route::put('organizations/{organization}', [OrganizationController::class, 'update']);
    Route::delete('organizations/{organization}', [OrganizationController::class, 'destroy']);

    // Organization membership
    Route::post('organizations/{organization}/join', [OrganizationController::class, 'join']);
    Route::post('organizations/{organization}/leave', [OrganizationController::class, 'leave']);

    // Organization member-only routes
    Route::middleware(['org.member:member'])->group(function () {
        Route::get('organizations/{organization}/members', [OrganizationController::class, 'members']);
    });

    // Organization admin-only routes
    Route::middleware(['org.member:admin'])->group(function () {
        Route::put('organizations/{organization}/members/{user}', [OrganizationController::class, 'updateMember']);
        Route::delete('organizations/{organization}/members/{user}', [OrganizationController::class, 'removeMember']);
    });
    
    // Posts - authenticated actions
    Route::post('posts', [PostController::class, 'store']);
    Route::put('posts/{post}', [PostController::class, 'update']);
    Route::delete('posts/{post}', [PostController::class, 'destroy']);
    
    // Comments - authenticated actions
    Route::post('comments', [CommentController::class, 'store']);
    Route::put('comments/{comment}', [CommentController::class, 'update']);
    Route::delete('comments/{comment}', [CommentController::class, 'destroy']);
    
    // Reactions - authenticated actions
    Route::post('reactions/toggle', [ReactionController::class, 'toggle']);
    
    // Courses - authenticated actions
    Route::middleware(['role:faculty,admin'])->group(function () {
        Route::post('courses', [CourseController::class, 'store']);
        Route::put('courses/{course}', [CourseController::class, 'update']);
        Route::get('courses/{course}/students', [CourseController::class, 'students']);
    });

    Route::middleware(['role:admin'])->group(function () {
        Route::delete('courses/{course}', [CourseController::class, 'destroy']);
    });

    Route::post('courses/{course}/enroll', [CourseController::class, 'enroll']);
    Route::post('courses/{course}/drop', [CourseController::class, 'drop']);

    // Notifications
    Route::get('notifications', [NotificationController::class, 'index']);
    Route::get('notifications/unread-count', [NotificationController::class, 'unreadCount']);
    Route::post('notifications/mark-all-read', [NotificationController::class, 'markAllAsRead']);
    Route::post('notifications/{notification}/mark-read', [NotificationController::class, 'markAsRead']);
    Route::delete('notifications/{notification}', [NotificationController::class, 'destroy']);
});
