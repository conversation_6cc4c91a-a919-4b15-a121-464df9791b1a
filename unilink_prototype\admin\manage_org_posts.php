<?php
// Include admin authentication check
require_once '../includes/admin_check.php';

// Database connection
$conn = mysqli_connect("localhost", "root", "", "unilink_db");

// Check connection
if (!$conn) {
    die("Connection failed: " . mysqli_connect_error());
}

// Get all posts from all organizations
$posts_query = "SELECT p.*, o.name as org_name, o.logo as org_logo 
                FROM posts p 
                JOIN organizations o ON p.organization_id = o.id 
                ORDER BY p.created_at DESC";
$posts_result = mysqli_query($conn, $posts_query);
$posts = [];
if ($posts_result) {
    $posts = mysqli_fetch_all($posts_result, MYSQLI_ASSOC);
}

// Handle post deletion
if (isset($_GET['action']) && $_GET['action'] == 'delete' && isset($_GET['id'])) {
    $post_id = intval($_GET['id']);
    
    // Delete the post
    $delete_query = "DELETE FROM posts WHERE id = $post_id";
    $delete_result = mysqli_query($conn, $delete_query);
    
    if ($delete_result) {
        header("Location: manage_org_posts.php?success=Post deleted successfully");
        exit();
    } else {
        $error = mysqli_error($conn);
        header("Location: manage_org_posts.php?error=Error deleting post: $error");
        exit();
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Manage Organization Posts - Admin Dashboard</title>
    <!-- Bootstrap CSS -->
    <link href="../assets/libraries/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="../assets/libraries/css/all.min.css" rel="stylesheet">
    <link rel="stylesheet" href="../assets/libraries/fontawesome/css/all.min.css">
    <!-- Custom CSS -->
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand fw-bold" href="../index.php">
                <span style="color: #7BC74D;">Uni</span><span style="color: #EEEEEE;">Link</span>
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.php"><i class="fas fa-home"></i> Home</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle active" href="#" id="adminDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user-shield"></i> Admin
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="adminDropdown">
                            <li><a class="dropdown-item" href="dashboard.php"><i class="fas fa-tachometer-alt me-2"></i>Dashboard</a></li>
                            <li><a class="dropdown-item" href="students.php"><i class="fas fa-user-graduate me-2"></i>Student Records</a></li>
                            <li><a class="dropdown-item" href="validation.php"><i class="fas fa-user-check me-2"></i>Validate Students</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="settings.php"><i class="fas fa-cog me-2"></i>Settings</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container py-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="fas fa-clipboard-list me-2"></i>Manage Organization Posts</h2>
            <a href="dashboard.php" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
            </a>
        </div>

        <?php if (isset($_GET['success'])): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <?php echo htmlspecialchars($_GET['success']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <?php if (isset($_GET['error'])): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <?php echo htmlspecialchars($_GET['error']); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        <?php endif; ?>

        <div class="card shadow-sm mb-4">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-clipboard me-2"></i>All Organization Posts</h5>
                <a href="../create_post.php" class="btn btn-sm btn-light">
                    <i class="fas fa-plus-circle me-1"></i>Create New Post
                </a>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-light">
                            <tr>
                                <th>Organization</th>
                                <th>Title</th>
                                <th>Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if(count($posts) > 0): ?>
                                <?php foreach($posts as $post): ?>
                                    <tr>
                                        <td class="align-middle">
                                            <div class="d-flex align-items-center">
                                                <img src="../<?php echo htmlspecialchars($post['org_logo']); ?>" class="rounded-circle me-2" width="30" height="30" alt="Logo">
                                                <span><?php echo htmlspecialchars($post['org_name']); ?></span>
                                            </div>
                                        </td>
                                        <td class="align-middle"><?php echo htmlspecialchars($post['title']); ?></td>
                                        <td class="align-middle"><?php echo date('M d, Y', strtotime($post['created_at'])); ?></td>
                                        <td class="align-middle">
                                            <div class="btn-group">
                                                <a href="../edit_post.php?id=<?php echo $post['id']; ?>" class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="../view_post.php?id=<?php echo $post['id']; ?>" class="btn btn-sm btn-outline-info">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="javascript:void(0)" onclick="confirmDelete(<?php echo $post['id']; ?>)" class="btn btn-sm btn-outline-danger">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            <?php else: ?>
                                <tr>
                                    <td colspan="4" class="text-center py-4">
                                        <i class="fas fa-clipboard-list fa-3x text-muted mb-3"></i>
                                        <h5>No Posts Found</h5>
                                        <p class="text-muted">There are no organization posts to display</p>
                                    </td>
                                </tr>
                            <?php endif; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS Bundle with Popper -->
    <script src="../assets/libraries/js/bootstrap.bundle.min.js"></script>
    <!-- Include the dark mode script -->
    <script src="../assets/js/main.js"></script>
    
    <script>
        function confirmDelete(postId) {
            if (confirm('Are you sure you want to delete this post? This action cannot be undone.')) {
                window.location.href = 'manage_org_posts.php?action=delete&id=' + postId;
            }
        }
    </script>
</body>
</html>