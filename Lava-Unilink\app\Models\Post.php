<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;

class Post extends Model
{
    protected $fillable = [
        'title',
        'content',
        'type',
        'media',
        'visibility',
        'is_pinned',
        'comments_enabled',
        'user_id',
        'organization_id',
        'published_at',
    ];

    protected $casts = [
        'media' => 'array',
        'is_pinned' => 'boolean',
        'comments_enabled' => 'boolean',
        'published_at' => 'datetime',
    ];

    /**
     * Get the user who created this post.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the organization this post belongs to.
     */
    public function organization(): BelongsTo
    {
        return $this->belongsTo(Organization::class);
    }

    /**
     * Get all comments for this post.
     */
    public function comments(): <PERSON><PERSON><PERSON>
    {
        return $this->hasMany(Comment::class);
    }

    /**
     * Get all reactions for this post.
     */
    public function reactions(): MorphMany
    {
        return $this->morphMany(Reaction::class, 'reactable');
    }

    /**
     * Get the count of reactions by type.
     */
    public function getReactionCounts(): array
    {
        return $this->reactions()
                    ->selectRaw('type, count(*) as count')
                    ->groupBy('type')
                    ->pluck('count', 'type')
                    ->toArray();
    }

    /**
     * Check if a user has reacted to this post.
     */
    public function hasUserReacted(User $user, string $type = null): bool
    {
        $query = $this->reactions()->where('user_id', $user->id);

        if ($type) {
            $query->where('type', $type);
        }

        return $query->exists();
    }

    /**
     * Scope for published posts.
     */
    public function scopePublished($query)
    {
        return $query->whereNotNull('published_at')
                    ->where('published_at', '<=', now());
    }

    /**
     * Scope for pinned posts.
     */
    public function scopePinned($query)
    {
        return $query->where('is_pinned', true);
    }
}
