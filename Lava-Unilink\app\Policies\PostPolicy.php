<?php

namespace App\Policies;

use App\Models\Post;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class PostPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return true; // All authenticated users can view posts
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Post $post): bool
    {
        // Public posts can be viewed by anyone
        if ($post->visibility === 'public') {
            return true;
        }

        // Private posts can only be viewed by the author or admins
        if ($post->visibility === 'private') {
            return $user->id === $post->user_id || $user->isAdmin();
        }

        // Members-only posts require organization membership
        if ($post->visibility === 'members_only' && $post->organization_id) {
            return $post->organization->hasMember($user) || $user->isAdmin();
        }

        return false;
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return true; // All authenticated users can create posts
    }

    /**
     * Determine whether the user can create posts for an organization.
     */
    public function createForOrganization(User $user, $organization): bool
    {
        if ($user->isAdmin()) {
            return true;
        }

        return $organization->hasMember($user);
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Post $post): bool
    {
        // Post author can always update
        if ($user->id === $post->user_id) {
            return true;
        }

        // Admins can update any post
        if ($user->isAdmin()) {
            return true;
        }

        // Organization admins can update posts in their organization
        if ($post->organization_id) {
            return $post->organization->hasAdmin($user);
        }

        return false;
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Post $post): bool
    {
        // Post author can always delete
        if ($user->id === $post->user_id) {
            return true;
        }

        // Admins can delete any post
        if ($user->isAdmin()) {
            return true;
        }

        // Organization admins can delete posts in their organization
        if ($post->organization_id) {
            return $post->organization->hasAdmin($user);
        }

        return false;
    }

    /**
     * Determine whether the user can pin/unpin the model.
     */
    public function pin(User $user, Post $post): bool
    {
        // Only admins and organization admins can pin posts
        if ($user->isAdmin()) {
            return true;
        }

        if ($post->organization_id) {
            return $post->organization->hasAdmin($user);
        }

        return false;
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Post $post): bool
    {
        return $user->isAdmin();
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Post $post): bool
    {
        return $user->isAdmin();
    }
}
