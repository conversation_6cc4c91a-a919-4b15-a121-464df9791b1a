<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->string('student_id')->nullable()->unique();
            $table->string('phone')->nullable();
            $table->date('birth_date')->nullable();
            $table->text('bio')->nullable();
            $table->string('avatar')->nullable();
            $table->string('campus')->nullable();
            $table->string('department')->nullable();
            $table->string('year_level')->nullable(); // Freshman, Sophomore, etc.
            $table->enum('role', ['student', 'faculty', 'admin'])->default('student');
            $table->json('social_links')->nullable(); // Facebook, Instagram, etc.
            $table->boolean('is_active')->default(true);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'student_id', 'phone', 'birth_date', 'bio', 'avatar',
                'campus', 'department', 'year_level', 'role',
                'social_links', 'is_active'
            ]);
        });
    }
};
