<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Reaction;
use App\Models\Post;
use App\Models\Comment;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;

class ReactionController extends Controller
{
    /**
     * Toggle a reaction on a post or comment.
     */
    public function toggle(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'reactable_type' => 'required|string|in:post,comment',
            'reactable_id' => 'required|integer',
            'type' => 'required|string|in:like,love,laugh,wow,sad,angry',
        ]);

        // Get the reactable model
        $reactableClass = $validated['reactable_type'] === 'post' ? Post::class : Comment::class;
        $reactable = $reactableClass::find($validated['reactable_id']);

        if (!$reactable) {
            return response()->json(['message' => 'Item not found'], 404);
        }

        // Check permissions for private/members-only content
        if ($validated['reactable_type'] === 'post') {
            $post = $reactable;
            if ($post->visibility === 'private' && $post->user_id !== Auth::id() && !Auth::user()->isAdmin()) {
                return response()->json(['message' => 'You cannot react to this post'], 403);
            }

            if ($post->visibility === 'members_only' && $post->organization_id) {
                $organization = $post->organization;
                if (!$organization->hasMember(Auth::user()) && !Auth::user()->isAdmin()) {
                    return response()->json(['message' => 'You cannot react to this post'], 403);
                }
            }
        } else {
            // For comments, check the post's visibility
            $post = $reactable->post;
            if ($post->visibility === 'private' && $post->user_id !== Auth::id() && !Auth::user()->isAdmin()) {
                return response()->json(['message' => 'You cannot react to this comment'], 403);
            }

            if ($post->visibility === 'members_only' && $post->organization_id) {
                $organization = $post->organization;
                if (!$organization->hasMember(Auth::user()) && !Auth::user()->isAdmin()) {
                    return response()->json(['message' => 'You cannot react to this comment'], 403);
                }
            }
        }

        // Check if user already has a reaction on this item
        $existingReaction = Reaction::where([
            'user_id' => Auth::id(),
            'reactable_type' => $reactableClass,
            'reactable_id' => $validated['reactable_id'],
        ])->first();

        if ($existingReaction) {
            if ($existingReaction->type === $validated['type']) {
                // Same reaction type - remove it (toggle off)
                $existingReaction->delete();
                $action = 'removed';
            } else {
                // Different reaction type - update it
                $existingReaction->update(['type' => $validated['type']]);
                $action = 'updated';
            }
        } else {
            // No existing reaction - create new one
            Reaction::create([
                'user_id' => Auth::id(),
                'reactable_type' => $reactableClass,
                'reactable_id' => $validated['reactable_id'],
                'type' => $validated['type'],
            ]);
            $action = 'added';
        }

        // Get updated reaction counts
        $reactionCounts = $reactable->getReactionCounts();

        return response()->json([
            'message' => "Reaction {$action} successfully",
            'action' => $action,
            'reaction_counts' => $reactionCounts,
            'user_reaction' => $action !== 'removed' ? $validated['type'] : null,
        ]);
    }

    /**
     * Get reactions for a specific post or comment.
     */
    public function index(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'reactable_type' => 'required|string|in:post,comment',
            'reactable_id' => 'required|integer',
            'type' => 'nullable|string|in:like,love,laugh,wow,sad,angry',
        ]);

        $reactableClass = $validated['reactable_type'] === 'post' ? Post::class : Comment::class;

        $query = Reaction::where([
            'reactable_type' => $reactableClass,
            'reactable_id' => $validated['reactable_id'],
        ])->with('user');

        if (isset($validated['type'])) {
            $query->where('type', $validated['type']);
        }

        $reactions = $query->latest()->paginate($request->get('per_page', 20));

        return response()->json($reactions);
    }

    /**
     * Get reaction summary for a post or comment.
     */
    public function summary(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'reactable_type' => 'required|string|in:post,comment',
            'reactable_id' => 'required|integer',
        ]);

        $reactableClass = $validated['reactable_type'] === 'post' ? Post::class : Comment::class;
        $reactable = $reactableClass::find($validated['reactable_id']);

        if (!$reactable) {
            return response()->json(['message' => 'Item not found'], 404);
        }

        $reactionCounts = $reactable->getReactionCounts();
        $userReaction = null;

        // Get current user's reaction if authenticated
        if (Auth::check()) {
            $userReactionModel = Reaction::where([
                'user_id' => Auth::id(),
                'reactable_type' => $reactableClass,
                'reactable_id' => $validated['reactable_id'],
            ])->first();

            $userReaction = $userReactionModel ? $userReactionModel->type : null;
        }

        return response()->json([
            'reaction_counts' => $reactionCounts,
            'user_reaction' => $userReaction,
            'total_reactions' => array_sum($reactionCounts),
        ]);
    }

    /**
     * Get available reaction types.
     */
    public function types(): JsonResponse
    {
        return response()->json([
            'types' => Reaction::getTypes()
        ]);
    }
}
