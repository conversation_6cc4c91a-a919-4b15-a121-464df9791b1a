<?php
// Start session
session_start();

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] != 'admin') {
    header("Location: ../login.php");
    exit();
}

// Database connection
$conn = mysqli_connect("localhost", "root", "", "unilink_db");

// Check connection
if (!$conn) {
    die("Connection failed: " . mysqli_connect_error());
}

// Initialize variables
$student = null;
$error = "";

// Check if student ID is provided
if (isset($_GET['id'])) {
    $user_id = mysqli_real_escape_string($conn, $_GET['id']);
    
    // Get student information
    $query = "SELECT u.*, sr.* 
              FROM users u 
              LEFT JOIN student_records sr ON u.username = sr.student_id 
              WHERE u.id = '$user_id'";
    $result = mysqli_query($conn, $query);
    
    if (mysqli_num_rows($result) > 0) {
        $student = mysqli_fetch_assoc($result);
    } else {
        $error = "Student not found.";
    }
} else {
    $error = "No student ID provided.";
}

// Handle student validation
if (isset($_POST['validate_student']) && isset($student)) {
    $student_id = mysqli_real_escape_string($conn, $student['student_id']);
    $admin_id = $_SESSION['user_id'];
    
    $update_query = "UPDATE student_records SET 
                    is_validated = TRUE, 
                    validated_by = '$admin_id', 
                    validated_at = NOW() 
                    WHERE student_id = '$student_id'";
    
    if (mysqli_query($conn, $update_query)) {
        $success = "Student ID $student_id has been successfully validated.";
        // Refresh student data
        $result = mysqli_query($conn, $query);
        $student = mysqli_fetch_assoc($result);
    } else {
        $error = "Error validating student: " . mysqli_error($conn);
    }
}

mysqli_close($conn);
?>

<!DOCTYPE html>
<html lang="en" data-bs-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>View Student Profile - UniLink Admin</title>
    <!-- Bootstrap CSS -->
    <link href="../assets/libraries/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="../assets/libraries/fontawesome/css/all.min.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="../assets/css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
        <div class="container">
            <a class="navbar-brand fw-bold" href="../index.php">
                <span style="color: #7BC74D;">Uni</span><span style="color: #EEEEEE;">Link</span>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.php"><i class="fas fa-home"></i> Home</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle active" href="#" id="adminDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user-shield"></i> Admin
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="adminDropdown">
                            <li><a class="dropdown-item" href="dashboard.php"><i class="fas fa-tachometer-alt me-2"></i>Dashboard</a></li>
                            <li><a class="dropdown-item" href="students.php"><i class="fas fa-user-graduate me-2"></i>Student Records</a></li>
                            <li><a class="dropdown-item active" href="validation.php"><i class="fas fa-user-check me-2"></i>Validate Students</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="settings.php"><i class="fas fa-cog me-2"></i>Settings</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Add dark mode toggle button to the navbar -->
    <div class="dropdown ms-2">
        <button class="btn btn-outline-light btn-sm dropdown-toggle" type="button" id="bd-theme" data-bs-toggle="dropdown" aria-expanded="false">
            <i class="fas fa-sun theme-icon-active" id="theme-icon"></i>
            <span class="d-none d-sm-inline ms-1" id="bd-theme-text">Toggle theme</span>
        </button>
        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="bd-theme">
            <li>
                <button type="button" class="dropdown-item d-flex align-items-center" data-bs-theme-value="light">
                    <i class="fas fa-sun me-2"></i>Light
                </button>
            </li>
            <li>
                <button type="button" class="dropdown-item d-flex align-items-center" data-bs-theme-value="dark">
                    <i class="fas fa-moon me-2"></i>Dark
                </button>
            </li>
            <li>
                <button type="button" class="dropdown-item d-flex align-items-center" data-bs-theme-value="auto">
                    <i class="fas fa-adjust me-2"></i>Auto
                </button>
            </li>
        </ul>
    </div>

    <!-- Main Content -->
    <div class="container mt-4">
        <?php if (!empty($error)): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle me-2"></i><?php echo $error; ?>
            </div>
            <div class="text-center mt-3">
                <a href="validation.php" class="btn btn-primary">
                    <i class="fas fa-arrow-left me-2"></i>Back to Validation
                </a>
            </div>
        <?php elseif ($student): ?>
            <?php if (isset($success)): ?>
                <div class="alert alert-success">
                    <i class="fas fa-check-circle me-2"></i><?php echo $success; ?>
                </div>
            <?php endif; ?>
            
            <div class="row">
                <!-- Student Profile Card -->
                <div class="col-md-4 mb-4">
                    <div class="card shadow-sm">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0"><i class="fas fa-user me-2"></i>Student Profile</h5>
                        </div>
                        <div class="card-body text-center">
                            <img src="<?php echo !empty($student['profile_pic']) ? '../' . $student['profile_pic'] : '../img/profilepic.jpg'; ?>" 
                                 alt="Profile Picture" class="img-fluid rounded-circle mb-3" 
                                 style="width: 150px; height: 150px; object-fit: cover;">
                            
                            <h5><?php echo htmlspecialchars($student['full_name']); ?></h5>
                            <p class="text-muted mb-2"><?php echo htmlspecialchars($student['student_id']); ?></p>
                            
                            <?php if ($student['is_validated']): ?>
                                <div class="badge bg-success p-2 mb-3">
                                    <i class="fas fa-check-circle me-1"></i>Validated
                                </div>
                            <?php else: ?>
                                <div class="badge bg-danger p-2 mb-3">
                                    <i class="fas fa-times-circle me-1"></i>Not Validated
                                </div>
                                
                                <form method="POST" class="d-grid gap-2">
                                    <button type="submit" name="validate_student" class="btn btn-success">
                                        <i class="fas fa-user-check me-2"></i>Validate Student
                                    </button>
                                </form>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                
                <!-- Student Details -->
                <div class="col-md-8">
                    <div class="card shadow-sm mb-4">
                        <div class="card-header bg-primary text-white">
                            <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Student Information</h5>
                        </div>
                        <div class="card-body">
                            <div class="row mb-3">
                                <div class="col-md-4 fw-bold">Student ID:</div>
                                <div class="col-md-8"><?php echo htmlspecialchars($student['student_id']); ?></div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-4 fw-bold">Full Name:</div>
                                <div class="col-md-8"><?php echo htmlspecialchars($student['full_name']); ?></div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-4 fw-bold">Email:</div>
                                <div class="col-md-8">
                                    <?php if (!empty($student['email'])): ?>
                                        <a href="mailto:<?php echo htmlspecialchars($student['email']); ?>">
                                            <?php echo htmlspecialchars($student['email']); ?>
                                        </a>
                                    <?php else: ?>
                                        <span class="text-muted">Not provided</span>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-4 fw-bold">School Email:</div>
                                <div class="col-md-8">
                                    <?php if (!empty($student['school_email'])): ?>
                                        <a href="mailto:<?php echo htmlspecialchars($student['school_email']); ?>">
                                            <?php echo htmlspecialchars($student['school_email']); ?>
                                        </a>
                                    <?php else: ?>
                                        <span class="text-muted">Not provided</span>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-4 fw-bold">Mobile Number:</div>
                                <div class="col-md-8">
                                    <?php echo !empty($student['mobile']) ? htmlspecialchars($student['mobile']) : '<span class="text-muted">Not provided</span>'; ?>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-4 fw-bold">Birth Date:</div>
                                <div class="col-md-8">
                                    <?php echo !empty($student['birth_date']) ? date('F d, Y', strtotime($student['birth_date'])) : '<span class="text-muted">Not provided</span>'; ?>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-4 fw-bold">Address:</div>
                                <div class="col-md-8">
                                    <?php echo !empty($student['address']) ? nl2br(htmlspecialchars($student['address'])) : '<span class="text-muted">Not provided</span>'; ?>
                                </div>
                            </div>
                            <div class="row mb-3">
                                <div class="col-md-4 fw-bold">Registered On:</div>
                                <div class="col-md-8">
                                    <?php echo !empty($student['created_at']) ? date('F d, Y', strtotime($student['created_at'])) : '<span class="text-muted">Unknown</span>'; ?>
                                </div>
                            </div>
                            <?php if ($student['is_validated']): ?>
                            <div class="row mb-3">
                                <div class="col-md-4 fw-bold">Validated On:</div>
                                <div class="col-md-8">
                                    <?php echo date('F d, Y', strtotime($student['validated_at'])); ?>
                                </div>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                        <a href="validation.php" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Back to Validation
                        </a>
                        <?php if (!$student['is_validated']): ?>
                        <form method="POST" class="d-inline">
                            <button type="submit" name="validate_student" class="btn btn-success">
                                <i class="fas fa-user-check me-2"></i>Validate Student
                            </button>
                        </form>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
    
    <!-- Bootstrap JS Bundle with Popper -->
    <script src="../assets/libraries/js/bootstrap.bundle.min.js"></script>
    <!-- Include the dark mode script -->
    <script src="../assets/js/main.js"></script>
</body>
</html>
