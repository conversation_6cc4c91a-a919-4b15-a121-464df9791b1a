<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Organization;
use App\Models\OrganizationMember;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;

class OrganizationController extends Controller
{
    /**
     * Display a listing of organizations.
     */
    public function index(Request $request): JsonResponse
    {
        $query = Organization::with(['creator', 'members'])
                            ->withCount(['members', 'posts']);

        // Filter by status
        if ($request->has('status')) {
            $query->where('status', $request->status);
        }

        // Filter by type
        if ($request->has('type')) {
            $query->where('type', $request->type);
        }

        // Filter by campus
        if ($request->has('campus')) {
            $query->where('campus', $request->campus);
        }

        // Search by name or description
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        $organizations = $query->paginate($request->get('per_page', 15));

        return response()->json($organizations);
    }

    /**
     * Store a newly created organization.
     */
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:organizations',
            'description' => 'nullable|string',
            'type' => 'required|string|max:255',
            'email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:20',
            'website' => 'nullable|url|max:255',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'banner_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:5120',
            'address' => 'nullable|string',
            'campus' => 'nullable|string|max:255',
        ]);

        // Handle file uploads
        if ($request->hasFile('logo')) {
            $validated['logo'] = $request->file('logo')->store('organizations/logos', 'public');
        }

        if ($request->hasFile('banner_image')) {
            $validated['banner_image'] = $request->file('banner_image')->store('organizations/banners', 'public');
        }

        $validated['created_by'] = Auth::id();
        $validated['status'] = 'pending'; // Organizations need approval

        $organization = Organization::create($validated);

        // Make the creator an admin member
        OrganizationMember::create([
            'user_id' => Auth::id(),
            'organization_id' => $organization->id,
            'role' => 'admin',
            'status' => 'active',
            'joined_at' => now(),
        ]);

        return response()->json([
            'message' => 'Organization created successfully',
            'organization' => $organization->load(['creator', 'members'])
        ], 201);
    }

    /**
     * Display the specified organization.
     */
    public function show(Organization $organization): JsonResponse
    {
        $organization->load([
            'creator',
            'members' => function ($query) {
                $query->where('status', 'active')->with('user');
            },
            'posts' => function ($query) {
                $query->published()->latest()->take(5)->with(['user', 'reactions']);
            }
        ]);

        $organization->loadCount(['members', 'posts']);

        return response()->json($organization);
    }

    /**
     * Update the specified organization.
     */
    public function update(Request $request, Organization $organization): JsonResponse
    {
        // Check if user can update this organization
        if (!$organization->hasAdmin(Auth::user()) && !Auth::user()->isAdmin()) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $validated = $request->validate([
            'name' => ['required', 'string', 'max:255', Rule::unique('organizations')->ignore($organization->id)],
            'description' => 'nullable|string',
            'type' => 'required|string|max:255',
            'email' => 'nullable|email|max:255',
            'phone' => 'nullable|string|max:20',
            'website' => 'nullable|url|max:255',
            'logo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'banner_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:5120',
            'address' => 'nullable|string',
            'campus' => 'nullable|string|max:255',
        ]);

        // Handle file uploads
        if ($request->hasFile('logo')) {
            if ($organization->logo) {
                Storage::disk('public')->delete($organization->logo);
            }
            $validated['logo'] = $request->file('logo')->store('organizations/logos', 'public');
        }

        if ($request->hasFile('banner_image')) {
            if ($organization->banner_image) {
                Storage::disk('public')->delete($organization->banner_image);
            }
            $validated['banner_image'] = $request->file('banner_image')->store('organizations/banners', 'public');
        }

        $organization->update($validated);

        return response()->json([
            'message' => 'Organization updated successfully',
            'organization' => $organization->load(['creator', 'members'])
        ]);
    }

    /**
     * Remove the specified organization.
     */
    public function destroy(Organization $organization): JsonResponse
    {
        // Only admins or organization creator can delete
        if (!$organization->hasAdmin(Auth::user()) && !Auth::user()->isAdmin()) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        // Delete associated files
        if ($organization->logo) {
            Storage::disk('public')->delete($organization->logo);
        }
        if ($organization->banner_image) {
            Storage::disk('public')->delete($organization->banner_image);
        }

        $organization->delete();

        return response()->json(['message' => 'Organization deleted successfully']);
    }

    /**
     * Join an organization.
     */
    public function join(Organization $organization): JsonResponse
    {
        $user = Auth::user();

        // Check if already a member
        if ($organization->hasMember($user)) {
            return response()->json(['message' => 'Already a member of this organization'], 400);
        }

        // Check if organization is active
        if ($organization->status !== 'active') {
            return response()->json(['message' => 'Organization is not accepting new members'], 400);
        }

        OrganizationMember::create([
            'user_id' => $user->id,
            'organization_id' => $organization->id,
            'role' => 'member',
            'status' => 'pending', // Requires approval
        ]);

        return response()->json(['message' => 'Membership request sent successfully']);
    }

    /**
     * Leave an organization.
     */
    public function leave(Organization $organization): JsonResponse
    {
        $user = Auth::user();

        $membership = OrganizationMember::where([
            'user_id' => $user->id,
            'organization_id' => $organization->id,
        ])->first();

        if (!$membership) {
            return response()->json(['message' => 'Not a member of this organization'], 400);
        }

        // Check if user is the creator/last admin
        $adminCount = OrganizationMember::where([
            'organization_id' => $organization->id,
            'role' => 'admin',
            'status' => 'active',
        ])->count();

        if ($membership->role === 'admin' && $adminCount <= 1) {
            return response()->json(['message' => 'Cannot leave organization as the last admin'], 400);
        }

        $membership->delete();

        return response()->json(['message' => 'Successfully left the organization']);
    }

    /**
     * Get organization members.
     */
    public function members(Organization $organization): JsonResponse
    {
        $members = $organization->memberships()
                               ->with('user')
                               ->where('status', 'active')
                               ->orderBy('role', 'desc')
                               ->orderBy('joined_at', 'asc')
                               ->get();

        return response()->json($members);
    }

    /**
     * Update a member's role or status.
     */
    public function updateMember(Request $request, Organization $organization, $userId): JsonResponse
    {
        // Check if user is admin of this organization
        if (!$organization->hasAdmin(Auth::user()) && !Auth::user()->isAdmin()) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $validated = $request->validate([
            'role' => 'nullable|string|in:member,moderator,admin',
            'status' => 'nullable|string|in:active,inactive,pending',
        ]);

        $membership = OrganizationMember::where([
            'user_id' => $userId,
            'organization_id' => $organization->id,
        ])->first();

        if (!$membership) {
            return response()->json(['message' => 'Member not found'], 404);
        }

        // Prevent removing the last admin
        if (isset($validated['role']) && $membership->role === 'admin' && $validated['role'] !== 'admin') {
            $adminCount = OrganizationMember::where([
                'organization_id' => $organization->id,
                'role' => 'admin',
                'status' => 'active',
            ])->count();

            if ($adminCount <= 1) {
                return response()->json(['message' => 'Cannot demote the last admin'], 400);
            }
        }

        // Set joined_at when approving a pending member
        if (isset($validated['status']) && $validated['status'] === 'active' && $membership->status === 'pending') {
            $validated['joined_at'] = now();
        }

        $membership->update($validated);

        return response()->json([
            'message' => 'Member updated successfully',
            'member' => $membership->load('user')
        ]);
    }

    /**
     * Remove a member from the organization.
     */
    public function removeMember(Organization $organization, $userId): JsonResponse
    {
        // Check if user is admin of this organization
        if (!$organization->hasAdmin(Auth::user()) && !Auth::user()->isAdmin()) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $membership = OrganizationMember::where([
            'user_id' => $userId,
            'organization_id' => $organization->id,
        ])->first();

        if (!$membership) {
            return response()->json(['message' => 'Member not found'], 404);
        }

        // Prevent removing the last admin
        if ($membership->role === 'admin') {
            $adminCount = OrganizationMember::where([
                'organization_id' => $organization->id,
                'role' => 'admin',
                'status' => 'active',
            ])->count();

            if ($adminCount <= 1) {
                return response()->json(['message' => 'Cannot remove the last admin'], 400);
            }
        }

        $membership->delete();

        return response()->json(['message' => 'Member removed successfully']);
    }
}
