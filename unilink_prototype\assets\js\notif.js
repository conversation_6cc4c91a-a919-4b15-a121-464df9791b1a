document.addEventListener('DOMContentLoaded', function() {
    const notificationIcon = document.getElementById('notification-icon');
    const notificationPopup = document.getElementById('notification-popup');
    const closePopup = document.getElementById('close-popup');
    
    if (notificationIcon && notificationPopup && closePopup) {
        notificationIcon.addEventListener('click', function(event) {
            event.preventDefault(); // Prevent default link behavior
            console.log('Notification icon clicked');
            notificationPopup.style.display = 'block';  // Make the popup visible
            setTimeout(function() {
                notificationPopup.style.right = '0';  // Slide it in
            }, 10);
        });
        
        closePopup.addEventListener('click', function() {
            console.log('Close button clicked');
            notificationPopup.style.right = '-300px';  // Slide it out
            setTimeout(function() {
                notificationPopup.style.display = 'none';  // Hide it after sliding out
            }, 300); // Wait for the animation to complete before hiding
        });
        
        // Close popup when clicking outside
        document.addEventListener('click', function(event) {
            if (notificationPopup.style.display === 'block' && 
                !notificationPopup.contains(event.target) && 
                event.target !== notificationIcon &&
                !notificationIcon.contains(event.target)) {
                notificationPopup.style.right = '-300px';
                setTimeout(function() {
                    notificationPopup.style.display = 'none';
                }, 300);
            }
        });
    } else {
        console.error('Notification elements not found in the DOM');
    }
});
