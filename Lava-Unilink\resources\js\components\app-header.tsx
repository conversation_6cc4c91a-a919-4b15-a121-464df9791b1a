import { AppLogo } from '@/components/app-logo';
import { But<PERSON> } from '@/components/ui/button';
import { UserNav } from '@/components/user-nav';
import { type BreadcrumbItem } from '@/types';
import { Link } from '@inertiajs/react';
import { Menu } from 'lucide-react';

interface AppHeaderProps {
    breadcrumbs?: BreadcrumbItem[];
}

export function AppHeader({ breadcrumbs = [] }: AppHeaderProps) {
    return (
        <header className="sticky top-0 z-30 flex h-16 w-full items-center border-b bg-unilink-lightest dark:bg-unilink-darkest dark:border-unilink-secondary">
            <div className="container flex items-center justify-between">
                <div className="flex items-center gap-4">
                    <Button variant="ghost" size="icon" className="md:hidden">
                        <Menu className="h-5 w-5" />
                        <span className="sr-only">Toggle menu</span>
                    </Button>
                    <div className="flex items-center md:hidden">
                        <AppLogo className="h-8 w-8 text-unilink-primary" />
                        <span className="ml-2 text-xl font-semibold text-unilink-darkest dark:text-unilink-lightest">UniLink</span>
                    </div>
                    {breadcrumbs.length > 0 && (
                        <nav className="hidden md:flex">
                            <ol className="flex items-center space-x-2">
                                {breadcrumbs.map((item, index) => (
                                    <li key={index} className="flex items-center">
                                        {index > 0 && <span className="mx-2 text-muted-foreground">/</span>}
                                        {item.href ? (
                                            <Link
                                                href={item.href}
                                                className="text-sm font-medium text-unilink-secondary hover:text-unilink-primary dark:text-unilink-lightest dark:hover:text-unilink-primary"
                                            >
                                                {item.label}
                                            </Link>
                                        ) : (
                                            <span className="text-sm font-medium text-muted-foreground">{item.label}</span>
                                        )}
                                    </li>
                                ))}
                            </ol>
                        </nav>
                    )}
                </div>
                <div className="flex items-center gap-2">
                    <UserNav />
                </div>
            </div>
        </header>
    );
}

