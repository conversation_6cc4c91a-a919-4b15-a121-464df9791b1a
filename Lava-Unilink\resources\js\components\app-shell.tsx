import { cn } from '@/lib/utils';
import { type PropsWithChildren } from 'react';

interface AppShellProps extends PropsWithChildren {
    variant?: 'default' | 'sidebar';
}

export function AppShell({ children, variant = 'default' }: AppShellProps) {
    return (
        <div
            className={cn(
                'flex min-h-screen flex-col bg-unilink-lightest dark:bg-unilink-darkest',
                variant === 'sidebar' && 'md:pl-64'
            )}
        >
            {children}
        </div>
    );
}

