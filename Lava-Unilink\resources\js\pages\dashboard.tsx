import { useState, useEffect } from 'react';
import { Head, Link } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import PostCard from '@/components/post-card';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { type BreadcrumbItem } from '@/types';
import {
    Plus,
    Users,
    FileText,
    MessageCircle,
    BookOpen,
    TrendingUp
} from 'lucide-react';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/dashboard',
    },
];

interface DashboardProps {
    user?: any;
    userOrganizations?: any[];
    recentPosts?: any[];
    enrolledCourses?: any[];
    stats?: any;
    trendingOrganizations?: any[];
}

export default function Dashboard({
    user,
    userOrganizations = [],
    recentPosts = [],
    enrolledCourses = [],
    stats = {},
    trendingOrganizations = []
}: DashboardProps) {
    const [posts, setPosts] = useState(recentPosts);
    const [loading, setLoading] = useState(false);

    // Load dashboard data if not provided as props
    useEffect(() => {
        if (!user) {
            loadDashboardData();
        }
    }, []);

    const loadDashboardData = async () => {
        setLoading(true);
        try {
            const response = await fetch('/api/v1/dashboard/data');
            if (response.ok) {
                const data = await response.json();
                setPosts(data.posts?.data || []);
            }
        } catch (error) {
            console.error('Error loading dashboard data:', error);
        } finally {
            setLoading(false);
        }
    };

    const handleReaction = async (postId: number, reactionType: string) => {
        try {
            const response = await fetch('/api/v1/reactions/toggle', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
                },
                body: JSON.stringify({
                    reactable_type: 'post',
                    reactable_id: postId,
                    type: reactionType,
                }),
            });

            if (response.ok) {
                setPosts(prevPosts =>
                    prevPosts.map(post =>
                        post.id === postId
                            ? { ...post, reactions_count: post.reactions_count + (post.user_reacted ? -1 : 1) }
                            : post
                    )
                );
            }
        } catch (error) {
            console.error('Error toggling reaction:', error);
        }
    };

    const handleComment = (postId: number) => {
        window.location.href = `/posts/${postId}#comments`;
    };

    const handleShare = (postId: number) => {
        const url = `${window.location.origin}/posts/${postId}`;
        navigator.clipboard.writeText(url).then(() => {
            alert('Post URL copied to clipboard!');
        });
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Dashboard" />

            <div className="flex h-full flex-1 flex-col gap-6 rounded-xl p-6">
                {/* Welcome Header */}
                <div className="mb-4">
                    <h1 className="text-3xl font-bold text-gray-900">
                        Welcome to UniLink!
                    </h1>
                    <p className="text-gray-600 mt-1">
                        Connect with your university community
                    </p>
                </div>

                {/* Quick Stats */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <Card>
                        <CardContent className="p-4">
                            <div className="flex items-center">
                                <FileText className="w-8 h-8 text-blue-600" />
                                <div className="ml-3">
                                    <p className="text-sm font-medium text-gray-600">Posts</p>
                                    <p className="text-2xl font-bold text-gray-900">{stats?.total_posts || 0}</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="p-4">
                            <div className="flex items-center">
                                <MessageCircle className="w-8 h-8 text-green-600" />
                                <div className="ml-3">
                                    <p className="text-sm font-medium text-gray-600">Comments</p>
                                    <p className="text-2xl font-bold text-gray-900">{stats?.total_comments || 0}</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="p-4">
                            <div className="flex items-center">
                                <Users className="w-8 h-8 text-purple-600" />
                                <div className="ml-3">
                                    <p className="text-sm font-medium text-gray-600">Organizations</p>
                                    <p className="text-2xl font-bold text-gray-900">{stats?.organizations_count || 0}</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>

                    <Card>
                        <CardContent className="p-4">
                            <div className="flex items-center">
                                <BookOpen className="w-8 h-8 text-orange-600" />
                                <div className="ml-3">
                                    <p className="text-sm font-medium text-gray-600">Courses</p>
                                    <p className="text-2xl font-bold text-gray-900">{stats?.courses_count || 0}</p>
                                </div>
                            </div>
                        </CardContent>
                    </Card>
                </div>

                {/* Quick Actions */}
                <Card>
                    <CardHeader>
                        <CardTitle>Quick Actions</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="flex flex-wrap gap-3">
                            <Link href="/posts/create">
                                <Button>
                                    <Plus className="w-4 h-4 mr-2" />
                                    Create Post
                                </Button>
                            </Link>
                            <Link href="/organizations/create">
                                <Button variant="outline">
                                    <Users className="w-4 h-4 mr-2" />
                                    Create Organization
                                </Button>
                            </Link>
                            <Link href="/organizations">
                                <Button variant="outline">
                                    <Users className="w-4 h-4 mr-2" />
                                    Browse Organizations
                                </Button>
                            </Link>
                            <Link href="/courses">
                                <Button variant="outline">
                                    <BookOpen className="w-4 h-4 mr-2" />
                                    Browse Courses
                                </Button>
                            </Link>
                        </div>
                    </CardContent>
                </Card>

                {/* Recent Activity */}
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                    {/* Posts Feed */}
                    <div className="lg:col-span-2">
                        <div className="flex items-center justify-between mb-4">
                            <h2 className="text-xl font-semibold text-gray-900">Recent Posts</h2>
                            <Link href="/posts">
                                <Button variant="ghost" size="sm">
                                    View all posts
                                </Button>
                            </Link>
                        </div>

                        <div className="space-y-4">
                            {posts.length > 0 ? (
                                posts.slice(0, 5).map((post) => (
                                    <PostCard
                                        key={post.id}
                                        post={post}
                                        onReact={handleReaction}
                                        onComment={handleComment}
                                        onShare={handleShare}
                                    />
                                ))
                            ) : (
                                <Card>
                                    <CardContent className="p-8 text-center">
                                        <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                                        <h3 className="text-lg font-medium text-gray-900 mb-2">No posts yet</h3>
                                        <p className="text-gray-600 mb-4">
                                            Join some organizations to see posts in your feed.
                                        </p>
                                        <Link href="/organizations">
                                            <Button>Browse Organizations</Button>
                                        </Link>
                                    </CardContent>
                                </Card>
                            )}
                        </div>
                    </div>

                    {/* Sidebar */}
                    <div className="space-y-6">
                        {/* Organizations */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="text-lg">Organizations</CardTitle>
                            </CardHeader>
                            <CardContent>
                                {userOrganizations.length > 0 ? (
                                    <div className="space-y-3">
                                        {userOrganizations.slice(0, 3).map((org) => (
                                            <Link
                                                key={org.id}
                                                href={`/organizations/${org.id}`}
                                                className="flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-50 transition-colors"
                                            >
                                                <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                                                    <Users className="w-4 h-4 text-gray-500" />
                                                </div>
                                                <div className="flex-1 min-w-0">
                                                    <p className="text-sm font-medium text-gray-900 truncate">
                                                        {org.name}
                                                    </p>
                                                    <p className="text-xs text-gray-500">
                                                        {org.members_count} members
                                                    </p>
                                                </div>
                                            </Link>
                                        ))}
                                    </div>
                                ) : (
                                    <div className="text-center py-4">
                                        <Users className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                                        <p className="text-sm text-gray-600 mb-3">
                                            Join organizations to connect with peers
                                        </p>
                                        <Link href="/organizations">
                                            <Button size="sm">Browse Organizations</Button>
                                        </Link>
                                    </div>
                                )}
                            </CardContent>
                        </Card>

                        {/* Courses */}
                        <Card>
                            <CardHeader>
                                <CardTitle className="text-lg">Courses</CardTitle>
                            </CardHeader>
                            <CardContent>
                                {enrolledCourses.length > 0 ? (
                                    <div className="space-y-3">
                                        {enrolledCourses.slice(0, 3).map((course) => (
                                            <Link
                                                key={course.id}
                                                href={`/courses/${course.id}`}
                                                className="block p-2 rounded-lg hover:bg-gray-50 transition-colors"
                                            >
                                                <div className="flex items-center justify-between">
                                                    <div>
                                                        <p className="text-sm font-medium text-gray-900">
                                                            {course.code}
                                                        </p>
                                                        <p className="text-xs text-gray-600 truncate">
                                                            {course.name}
                                                        </p>
                                                    </div>
                                                    <Badge variant="secondary" className="text-xs">
                                                        {course.credits} cr
                                                    </Badge>
                                                </div>
                                            </Link>
                                        ))}
                                    </div>
                                ) : (
                                    <div className="text-center py-4">
                                        <BookOpen className="w-8 h-8 text-gray-400 mx-auto mb-2" />
                                        <p className="text-sm text-gray-600 mb-3">
                                            Enroll in courses to track your progress
                                        </p>
                                        <Link href="/courses">
                                            <Button size="sm">Browse Courses</Button>
                                        </Link>
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    </div>
                </div>
            </div>
        </AppLayout>
    );
}
