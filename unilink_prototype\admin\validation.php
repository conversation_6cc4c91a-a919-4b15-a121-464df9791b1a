<?php
// Start session
session_start();

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] != 'admin') {
    header("Location: ../login.php");
    exit();
}

// Database connection
$conn = mysqli_connect("localhost", "root", "", "unilink_db");

// Check connection
if (!$conn) {
    die("Connection failed: " . mysqli_connect_error());
}

// Initialize variables
$success = "";
$error = "";

// Handle student validation
if (isset($_POST['validate_student'])) {
    $student_id = mysqli_real_escape_string($conn, $_POST['student_id']);
    $admin_id = $_SESSION['user_id'];
    
    $update_query = "UPDATE student_records SET 
                    is_validated = TRUE, 
                    validated_by = '$admin_id', 
                    validated_at = NOW() 
                    WHERE student_id = '$student_id'";
    
    if (mysqli_query($conn, $update_query)) {
        $success = "Student ID $student_id has been successfully validated.";
    } else {
        $error = "Error validating student: " . mysqli_error($conn);
    }
}

// Get unvalidated students - Fix the column name from registered_at to created_at
$query = "SELECT sr.*, u.id as user_id, u.email, u.created_at 
          FROM student_records sr 
          LEFT JOIN users u ON sr.student_id = u.username 
          WHERE sr.is_validated = FALSE 
          ORDER BY u.created_at DESC";
$result = mysqli_query($conn, $query);
$students = [];

if ($result) {
    while ($row = mysqli_fetch_assoc($result)) {
        $students[] = $row;
    }
}

mysqli_close($conn);
?>

<!DOCTYPE html>
<html lang="en" data-bs-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Student Validation - UniLink Admin</title>
    <!-- Bootstrap 5 CSS -->
    <link rel="stylesheet" href="../assets/libraries/css/bootstrap.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="../assets/libraries/fontawesome/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body class="validation-page">
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
        <div class="container">
            <a class="navbar-brand fw-bold" href="../index.php">
                <span style="color: #7BC74D;">Uni</span><span style="color: #EEEEEE;">Link</span>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.php"><i class="fas fa-home"></i> Home</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle active" href="#" id="adminDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user-shield"></i> Admin
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="adminDropdown">
                            <li><a class="dropdown-item" href="dashboard.php"><i class="fas fa-tachometer-alt me-2"></i>Dashboard</a></li>
                            <li><a class="dropdown-item" href="students.php"><i class="fas fa-user-graduate me-2"></i>Student Records</a></li>
                            <li><a class="dropdown-item active" href="validation.php"><i class="fas fa-user-check me-2"></i>Validate Students</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="settings.php"><i class="fas fa-cog me-2"></i>Settings</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Page Header -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2 class="mb-0"><i class="fas fa-user-check text-primary me-2"></i>Student Validation</h2>
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb mb-0">
                    <li class="breadcrumb-item"><a href="../index.php">Home</a></li>
                    <li class="breadcrumb-item"><a href="dashboard.php">Admin</a></li>
                    <li class="breadcrumb-item active" aria-current="page">Validation</li>
                </ol>
            </nav>
        </div>
        
        <!-- Validation Stats -->
        <div class="validation-stats">
            <div class="row g-3">
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon"><i class="fas fa-user-graduate"></i></div>
                        <div class="stat-value"><?php echo count($students); ?></div>
                        <div class="stat-label">Pending Validations</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon"><i class="fas fa-check-circle"></i></div>
                        <div class="stat-value">
                            <?php 
                            // This would normally be a database query
                            echo rand(50, 200); 
                            ?>
                        </div>
                        <div class="stat-label">Validated Students</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon"><i class="fas fa-calendar-check"></i></div>
                        <div class="stat-value">
                            <?php 
                            // This would normally be a database query
                            echo rand(5, 20); 
                            ?>
                        </div>
                        <div class="stat-label">Validated Today</div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="stat-card">
                        <div class="stat-icon"><i class="fas fa-clock"></i></div>
                        <div class="stat-value">
                            <?php 
                            // This would normally be a database query
                            echo rand(1, 5) . "h"; 
                            ?>
                        </div>
                        <div class="stat-label">Avg. Response Time</div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Alerts -->
        <?php if (!empty($success)): ?>
            <div class="alert alert-success d-flex align-items-center" role="alert">
                <i class="fas fa-check-circle me-2 fs-5"></i>
                <div><?php echo $success; ?></div>
            </div>
        <?php endif; ?>
        
        <?php if (!empty($error)): ?>
            <div class="alert alert-danger d-flex align-items-center" role="alert">
                <i class="fas fa-exclamation-circle me-2 fs-5"></i>
                <div><?php echo $error; ?></div>
            </div>
        <?php endif; ?>
        
        <div class="row">
            <!-- Validation Table -->
            <div class="col-lg-9">
                <div class="card validation-card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white py-3">
                        <h5 class="mb-0"><i class="fas fa-user-check me-2"></i>Students Pending Validation</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($students)): ?>
                            <div class="alert alert-info d-flex align-items-center">
                                <i class="fas fa-info-circle me-2 fs-5"></i>
                                <div>No students pending validation at this time.</div>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover validation-table">
                                    <thead>
                                        <tr>
                                            <th>Student ID</th>
                                            <th>Full Name</th>
                                            <th>Email</th>
                                            <th>Registered On</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($students as $student): ?>
                                        <tr>
                                            <td>
                                                <span class="fw-medium"><?php echo $student['student_id']; ?></span>
                                            </td>
                                            <td><?php echo $student['full_name']; ?></td>
                                            <td>
                                                <?php if (isset($student['email'])): ?>
                                                    <a href="mailto:<?php echo $student['email']; ?>" class="text-decoration-none">
                                                        <?php echo $student['email']; ?>
                                                    </a>
                                                <?php else: ?>
                                                    <span class="text-muted">N/A</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <?php if (isset($student['created_at'])): ?>
                                                    <span class="badge bg-light text-dark validation-badge">
                                                        <i class="far fa-calendar-alt me-1"></i>
                                                        <?php echo date('M d, Y', strtotime($student['created_at'])); ?>
                                                    </span>
                                                <?php else: ?>
                                                    <span class="badge bg-secondary validation-badge">Not Registered</span>
                                                <?php endif; ?>
                                            </td>
                                            <td class="validation-actions">
                                                <form method="POST" class="d-inline">
                                                    <input type="hidden" name="student_id" value="<?php echo $student['student_id']; ?>">
                                                    <button type="submit" name="validate_student" class="btn btn-sm btn-success">
                                                        <i class="fas fa-check-circle me-1"></i>Validate
                                                    </button>
                                                </form>
                                                <?php if (isset($student['user_id'])): ?>
                                                <a href="view_student.php?id=<?php echo $student['user_id']; ?>" class="btn btn-sm btn-info ms-1">
                                                    <i class="fas fa-eye me-1"></i>View Profile
                                                </a>
                                                <?php endif; ?>
                                            </td>
                                        </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
            
            <!-- Guidelines -->
            <div class="col-lg-3">
                <div class="card validation-card shadow-sm mb-4">
                    <div class="card-header bg-primary text-white py-3">
                        <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Validation Guidelines</h5>
                    </div>
                    <div class="card-body">
                        <p class="lead">Before validating a student, please ensure:</p>
                        <ul class="validation-guidelines ps-0">
                            <li>The student ID matches the official university records</li>
                            <li>The student's full name is correctly formatted and matches records</li>
                            <li>The student has completed their profile with valid information</li>
                            <li>The student is currently enrolled in the university</li>
                        </ul>
                        <div class="alert alert-warning d-flex align-items-center mt-4">
                            <i class="fas fa-exclamation-triangle me-2 fs-5"></i>
                            <div>Validation grants students full access to the platform. Please verify carefully before approving.</div>
                        </div>
                    </div>
                </div>
                
                <!-- Quick Actions -->
                <div class="card validation-card shadow-sm">
                    <div class="card-header bg-primary text-white py-3">
                        <h5 class="mb-0"><i class="fas fa-bolt me-2"></i>Quick Actions</h5>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="students.php" class="btn btn-outline-primary">
                                <i class="fas fa-user-graduate me-2"></i>View All Students
                            </a>
                            <a href="reports.php" class="btn btn-outline-primary">
                                <i class="fas fa-chart-bar me-2"></i>Validation Reports
                            </a>
                            <a href="settings.php" class="btn btn-outline-primary">
                                <i class="fas fa-cog me-2"></i>Validation Settings
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS Bundle with Popper -->
    <script src="../assets/libraries/js/bootstrap.bundle.min.js"></script>
    <!-- Include the dark mode script -->
    <script src="../assets/js/main.js"></script>
    <script>
        // Enable tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl)
        })
        
        // Confirmation for validation
        document.querySelectorAll('button[name="validate_student"]').forEach(button => {
            button.addEventListener('click', function(e) {
                if (!confirm('Are you sure you want to validate this student?')) {
                    e.preventDefault();
                }
            });
        });
    </script>
</body>
</html>









