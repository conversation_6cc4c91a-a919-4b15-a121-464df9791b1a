<?php

namespace App\Http\Middleware;

use App\Models\Organization;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Symfony\Component\HttpFoundation\Response;

class OrganizationMemberMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     * @param  string  $role  Optional role requirement (admin, moderator, member)
     */
    public function handle(Request $request, Closure $next, string $role = 'member'): Response
    {
        if (!Auth::check()) {
            return response()->json(['message' => 'Unauthenticated'], 401);
        }

        $user = Auth::user();

        // Get organization ID from route parameters
        $organizationId = $request->route('organization');

        if ($organizationId instanceof Organization) {
            $organization = $organizationId;
        } else {
            $organization = Organization::find($organizationId);
        }

        if (!$organization) {
            return response()->json(['message' => 'Organization not found'], 404);
        }

        // Admin users can access everything
        if ($user->isAdmin()) {
            return $next($request);
        }

        // Check if user is a member of the organization
        $membership = $organization->memberships()
                                  ->where('user_id', $user->id)
                                  ->where('status', 'active')
                                  ->first();

        if (!$membership) {
            return response()->json(['message' => 'You are not a member of this organization'], 403);
        }

        // Check role requirements
        $roleHierarchy = ['member' => 1, 'moderator' => 2, 'admin' => 3];
        $requiredLevel = $roleHierarchy[$role] ?? 1;
        $userLevel = $roleHierarchy[$membership->role] ?? 1;

        if ($userLevel < $requiredLevel) {
            return response()->json([
                'message' => "Insufficient permissions. Required role: {$role}"
            ], 403);
        }

        return $next($request);
    }
}
