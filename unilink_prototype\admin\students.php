<?php
// Start session
session_start();

// Check if user is logged in and is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] != 'admin') {
    header("Location: ../login.php");
    exit();
}

// Database connection
$conn = mysqli_connect("localhost", "root", "", "unilink_db");

// Check connection
if (!$conn) {
    die("Connection failed: " . mysqli_connect_error());
}

// Handle student record actions
$success = $error = "";

// Add new student
if (isset($_POST['add_student'])) {
    $student_id = mysqli_real_escape_string($conn, $_POST['student_id']);
    $full_name = mysqli_real_escape_string($conn, $_POST['full_name']);
    
    // Check if student ID already exists
    $check_query = "SELECT * FROM student_records WHERE student_id = '$student_id'";
    $check_result = mysqli_query($conn, $check_query);
    
    if (mysqli_num_rows($check_result) > 0) {
        $error = "Student ID already exists in the system.";
    } else {
        $insert_query = "INSERT INTO student_records (student_id, full_name) VALUES ('$student_id', '$full_name')";
        
        if (mysqli_query($conn, $insert_query)) {
            $success = "Student record added successfully.";
        } else {
            $error = "Error: " . mysqli_error($conn);
        }
    }
}

// Delete student
if (isset($_GET['delete'])) {
    $student_id = mysqli_real_escape_string($conn, $_GET['delete']);
    
    $delete_query = "DELETE FROM student_records WHERE student_id = '$student_id'";
    
    if (mysqli_query($conn, $delete_query)) {
        $success = "Student record deleted successfully.";
    } else {
        $error = "Error: " . mysqli_error($conn);
    }
}

// Add this near the top of your PHP section to fetch student data for the modal
if (isset($_GET['get_student'])) {
    $student_id = mysqli_real_escape_string($conn, $_GET['get_student']);
    $query = "SELECT * FROM student_records WHERE student_id = '$student_id'";
    $result = mysqli_query($conn, $query);
    
    if (mysqli_num_rows($result) > 0) {
        $student_data = mysqli_fetch_assoc($result);
        echo json_encode($student_data);
        exit();
    }
    echo json_encode(['error' => 'Student not found']);
    exit();
}

// Add this to handle the edit form submission via AJAX
if (isset($_POST['edit_student'])) {
    $student_id = mysqli_real_escape_string($conn, $_POST['student_id']);
    $full_name = mysqli_real_escape_string($conn, $_POST['full_name']);
    $email = isset($_POST['email']) ? mysqli_real_escape_string($conn, $_POST['email']) : null;
    $is_validated = isset($_POST['is_validated']) ? 1 : 0;
    
    // Update student record
    $update_query = "UPDATE student_records SET full_name = '$full_name'";
    
    // Add email if provided
    if ($email) {
        $update_query .= ", email = '$email'";
    }
    
    // Handle validation status change
    $update_query .= ", is_validated = $is_validated";
    
    if ($is_validated) {
        $admin_id = $_SESSION['user_id'];
        $update_query .= ", validated_by = '$admin_id', validated_at = NOW()";
    } else {
        $update_query .= ", validated_by = NULL, validated_at = NULL";
    }
    
    $update_query .= " WHERE student_id = '$student_id'";
    
    if (mysqli_query($conn, $update_query)) {
        $success = "Student record updated successfully.";
    } else {
        $error = "Error updating record: " . mysqli_error($conn);
    }
}

// Get all student records
$query = "SELECT * FROM student_records ORDER BY full_name";
$result = mysqli_query($conn, $query);
$students = mysqli_fetch_all($result, MYSQLI_ASSOC);

mysqli_close($conn);
?>

<!DOCTYPE html>
<html lang="en" data-bs-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Student Records - UniLink Admin</title>
    <!-- Bootstrap 5 CSS -->
    <link rel="stylesheet" href="../assets/libraries/css/bootstrap.min.css">
    <!-- Font Awesome -->
    <link rel="stylesheet" href="../assets/libraries/fontawesome/css/all.min.css">
    <!-- Custom CSS -->
    <link rel="stylesheet" href="../assets/css/style.css">
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary sticky-top">
        <div class="container">
            <a class="navbar-brand fw-bold" href="../index.php">
                <span style="color: #7BC74D;">Uni</span><span style="color: #EEEEEE;">Link</span>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="../index.php"><i class="fas fa-home"></i> Home</a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle active" href="#" id="adminDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-user-shield"></i> Admin
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="adminDropdown">
                            <li><a class="dropdown-item" href="dashboard.php"><i class="fas fa-tachometer-alt me-2"></i>Dashboard</a></li>
                            <li><a class="dropdown-item active" href="students.php"><i class="fas fa-user-graduate me-2"></i>Student Records</a></li>
                            <li><a class="dropdown-item" href="validation.php"><i class="fas fa-user-check me-2"></i>Validate Students</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="settings.php"><i class="fas fa-cog me-2"></i>Settings</a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="../logout.php"><i class="fas fa-sign-out-alt"></i> Logout</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <div class="row">
            <div class="col-md-4 mb-4">
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white">
                        <h5 class="mb-0"><i class="fas fa-user-plus me-2"></i>Add Student Record</h5>
                    </div>
                    <div class="card-body">
                        <?php if (!empty($success)): ?>
                            <div class="alert alert-success"><?php echo $success; ?></div>
                        <?php endif; ?>
                        
                        <?php if (!empty($error)): ?>
                            <div class="alert alert-danger"><?php echo $error; ?></div>
                        <?php endif; ?>
                        
                        <form method="POST">
                            <div class="mb-3">
                                <label for="student_id" class="form-label">Student ID</label>
                                <input type="text" class="form-control" id="student_id" name="student_id" required>
                            </div>
                            <div class="mb-3">
                                <label for="full_name" class="form-label">Full Name</label>
                                <input type="text" class="form-control" id="full_name" name="full_name" required>
                                <small class="text-muted">Format: LASTNAME, FIRSTNAME M.</small>
                            </div>
                            <div class="d-grid">
                                <button type="submit" name="add_student" class="btn btn-primary">
                                    <i class="fas fa-plus-circle me-2"></i>Add Student
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
            
            <div class="col-md-8">
                <div class="card shadow-sm">
                    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="fas fa-user-graduate me-2"></i>Student Records</h5>
                        <div class="input-group" style="width: 250px;">
                            <input type="text" class="form-control form-control-sm" id="searchStudent" placeholder="Search students...">
                            <button class="btn btn-light btn-sm"><i class="fas fa-search"></i></button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>Student ID</th>
                                        <th>Full Name</th>
                                        <th>Validated</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($students as $student): ?>
                                    <tr>
                                        <td><?php echo $student['student_id']; ?></td>
                                        <td><?php echo $student['full_name']; ?></td>
                                        <td>
                                            <?php if ($student['is_validated']): ?>
                                                <span class="badge bg-success">Yes</span>
                                            <?php else: ?>
                                                <span class="badge bg-danger">No</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <div class="btn-group btn-group-sm">
                                                <a href="javascript:void(0)" onclick="editStudent('<?php echo $student['student_id']; ?>')" class="btn btn-outline-primary">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                <a href="javascript:void(0)" onclick="confirmDelete('<?php echo $student['student_id']; ?>')" class="btn btn-outline-danger">
                                                    <i class="fas fa-trash"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS Bundle with Popper -->
    <script src="../assets/libraries/js/bootstrap.bundle.min.js"></script>
    <!-- Include the dark mode script -->
    <script src="../assets/js/main.js"></script>
    
    <script>
    function confirmDelete(studentId) {
        if (confirm('Are you sure you want to delete this student record?')) {
            window.location.href = 'students.php?delete=' + studentId;
        }
    }
    
    // Simple search functionality
    document.getElementById('searchStudent').addEventListener('keyup', function() {
        const searchValue = this.value.toLowerCase();
        const tableRows = document.querySelectorAll('tbody tr');
        
        tableRows.forEach(row => {
            const studentId = row.cells[0].textContent.toLowerCase();
            const fullName = row.cells[1].textContent.toLowerCase();
            
            if (studentId.includes(searchValue) || fullName.includes(searchValue)) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
    });

    function editStudent(studentId) {
        // Fetch student data
        fetch('students.php?get_student=' + studentId)
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    alert('Error: ' + data.error);
                    return;
                }
                
                // Populate the form
                document.getElementById('edit_student_id').value = data.student_id;
                document.getElementById('display_student_id').value = data.student_id;
                document.getElementById('edit_full_name').value = data.full_name;
                document.getElementById('edit_email').value = data.email || '';
                
                const validationSwitch = document.getElementById('edit_is_validated');
                const validationLabel = document.getElementById('validation_status_label');
                const validationInfo = document.getElementById('validation_info');
                
                validationSwitch.checked = data.is_validated == 1;
                validationLabel.textContent = data.is_validated == 1 ? 'Validated' : 'Not Validated';
                
                if (data.is_validated == 1 && data.validated_at) {
                    validationInfo.textContent = 'Validated on ' + new Date(data.validated_at).toLocaleDateString();
                } else {
                    validationInfo.textContent = '';
                }
                
                // Add event listener for validation switch
                validationSwitch.addEventListener('change', function() {
                    validationLabel.textContent = this.checked ? 'Validated' : 'Not Validated';
                });
                
                // Show the modal
                const editModal = new bootstrap.Modal(document.getElementById('editStudentModal'));
                editModal.show();
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while fetching student data');
            });
    }
    </script>
</body>
</html>

<!-- Edit Student Modal -->
<div class="modal fade" id="editStudentModal" tabindex="-1" aria-labelledby="editStudentModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header bg-primary text-white">
        <h5 class="modal-title" id="editStudentModalLabel"><i class="fas fa-user-edit me-2"></i>Edit Student Record</h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <form id="editStudentForm" method="POST">
          <input type="hidden" id="edit_student_id" name="student_id">
          
          <div class="mb-3">
            <label class="form-label">Student ID</label>
            <input type="text" class="form-control-plaintext" id="display_student_id" readonly>
          </div>
          
          <div class="mb-3">
            <label for="edit_full_name" class="form-label">Full Name</label>
            <input type="text" class="form-control" id="edit_full_name" name="full_name" required>
            <small class="text-muted">Format: LASTNAME, FIRSTNAME M.</small>
          </div>
          
          <div class="mb-3">
            <label for="edit_email" class="form-label">Email (Optional)</label>
            <input type="email" class="form-control" id="edit_email" name="email">
          </div>
          
          <div class="mb-3">
            <div class="form-check form-switch">
              <input class="form-check-input" type="checkbox" id="edit_is_validated" name="is_validated">
              <label class="form-check-label" for="edit_is_validated" id="validation_status_label">
                Not Validated
              </label>
            </div>
            <small class="text-muted" id="validation_info"></small>
          </div>
          
          <input type="hidden" name="edit_student" value="1">
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
          <i class="fas fa-times me-1"></i>Cancel
        </button>
        <button type="submit" form="editStudentForm" class="btn btn-primary">
          <i class="fas fa-save me-1"></i>Save Changes
        </button>
      </div>
    </div>
  </div>
</div>




