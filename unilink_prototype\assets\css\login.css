:root {
    --color-lightest: #EEEEEE; /* Light Gray */
    --color-third-darkest: #7BC74D; /* Green */
    --color-second-darkest: #393E46; /* Dark Gray */
    --color-darkest: #222831; /* Dark Navy */
}

body {
    background-color: var(--color-lightest);
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    padding: 20px;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.login-container {
    max-width: 450px;
    width: 100%;
}

.login-card {
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.login-header {
    background-color: var(--color-third-darkest);
    color: white;
    padding: 1.5rem;
    text-align: center;
}

.login-body {
    padding: 2rem;
    background-color: white;
}

.login-footer {
    padding: 1.5rem;
    background-color: #f8f9fa;
    text-align: center;
    border-top: 1px solid #eee;
}

.form-floating {
    margin-bottom: 1.5rem;
}

.form-floating label {
    color: #6c757d;
}

.form-control:focus {
    border-color: var(--color-third-darkest);
    box-shadow: 0 0 0 0.25rem rgba(123, 199, 77, 0.25);
}

.btn-login {
    background-color: var(--color-third-darkest);
    border-color: var(--color-third-darkest);
    padding: 0.75rem;
    font-weight: 500;
    width: 100%;
    margin-top: 1rem;
}

.btn-login:hover {
    background-color: #6AB33C; /* Slightly darker green */
    border-color: #6AB33C;
}

a {
    color: var(--color-third-darkest);
    text-decoration: none;
}

a:hover {
    color: var(--color-second-darkest);
    text-decoration: underline;
}

.alert-danger {
    background-color: #f8d7da;
    border-color: #f5c2c7;
    color: #842029;
}

.form-check-input:checked {
    background-color: var(--color-third-darkest);
    border-color: var(--color-third-darkest);
}

/* Logo styling */
.login-logo {
    width: 80px;
    height: 80px;
    margin-bottom: 1rem;
}

/* Responsive adjustments */
@media (max-width: 576px) {
    .login-body {
        padding: 1.5rem;
    }
    
    .login-header {
        padding: 1.25rem;
    }
    
    .login-footer {
        padding: 1.25rem;
    }
}
