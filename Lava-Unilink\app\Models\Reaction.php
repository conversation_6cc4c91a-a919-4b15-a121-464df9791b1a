<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class Reaction extends Model
{
    protected $fillable = [
        'user_id',
        'reactable_id',
        'reactable_type',
        'type',
    ];

    /**
     * Get the user who made this reaction.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the reactable model (post or comment).
     */
    public function reactable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Available reaction types.
     */
    public static function getTypes(): array
    {
        return [
            'like' => '👍',
            'love' => '❤️',
            'laugh' => '😂',
            'wow' => '😮',
            'sad' => '😢',
            'angry' => '😠',
        ];
    }

    /**
     * Get the emoji for this reaction type.
     */
    public function getEmojiAttribute(): string
    {
        return self::getTypes()[$this->type] ?? '👍';
    }
}
