// Enhanced search functionality for posts
function searchPosts(searchTerm) {
    const posts = document.querySelectorAll('.post');
    const noResultsMessage = document.getElementById('no-search-results');
    let resultsFound = false;
  
    // Hide all posts initially
    posts.forEach(post => {
        post.style.display = 'none';
    });
  
    if (searchTerm.trim() === '') {
        // If search is empty, show all posts
        posts.forEach(post => {
            post.style.display = 'block';
        });
        
        // Hide no results message if it exists
        if (noResultsMessage) {
            noResultsMessage.style.display = 'none';
        }
        
        return;
    }
  
    // Show matching posts
    posts.forEach(post => {
        const title = post.querySelector('.card-title').textContent.toLowerCase();
        const description = post.querySelector('.card-text').textContent.toLowerCase();
        const searchLower = searchTerm.toLowerCase();
        
        if (title.includes(searchLower) || description.includes(searchLower)) {
            post.style.display = 'block';
            resultsFound = true;
        }
    });
    
    // Show or hide no results message
    if (noResultsMessage) {
        noResultsMessage.style.display = resultsFound ? 'none' : 'block';
    } else if (!resultsFound) {
        // Create no results message if it doesn't exist
        const feedContent = document.querySelector('.feed-content');
        if (feedContent) {
            const message = document.createElement('div');
            message.id = 'no-search-results';
            message.className = 'alert alert-info text-center my-4';
            message.innerHTML = `
                <i class="fas fa-search fa-2x mb-3"></i>
                <h5>No results found</h5>
                <p>No posts match your search for "${searchTerm}"</p>
                <button class="btn btn-outline-primary btn-sm mt-2" onclick="clearSearch()">
                    <i class="fas fa-times me-1"></i>Clear Search
                </button>
            `;
            feedContent.prepend(message);
        }
    }
    
    // Update search input value
    const searchInput = document.querySelector('input[type="search"]');
    if (searchInput && searchInput.value !== searchTerm) {
        searchInput.value = searchTerm;
    }
    
    console.log(`Search completed for: "${searchTerm}". Results found: ${resultsFound}`);
}

// Function to clear search
function clearSearch() {
    const searchInput = document.querySelector('input[type="search"]');
    if (searchInput) {
        searchInput.value = '';
        searchPosts('');
    }
}

// Handle search form submission
document.addEventListener('DOMContentLoaded', function() {
    const searchForm = document.querySelector('.search-container form');
    if (searchForm) {
        searchForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const searchInput = this.querySelector('input[type="search"]');
            if (searchInput) {
                searchPosts(searchInput.value);
            }
        });
    }
});

// Dark mode functionality
document.addEventListener('DOMContentLoaded', function() {
    // Get all theme toggle buttons
    const themeTogglers = document.querySelectorAll('[data-bs-theme-value]');
    
    // Check for saved theme preference or use the browser preference
    const getStoredTheme = () => localStorage.getItem('theme');
    const getPreferredTheme = () => {
        const storedTheme = getStoredTheme();
        if (storedTheme) {
            return storedTheme;
        }
        return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
    };
    
    // Set theme
    const setTheme = (theme) => {
        document.documentElement.setAttribute('data-bs-theme', theme);
        localStorage.setItem('theme', theme);
        
        // Set cookie for PHP to read
        document.cookie = `theme=${theme}; path=/; max-age=31536000`; // 1 year
        
        // Update theme icon
        updateThemeIcon(theme);
    };
    
    // Apply the theme
    const theme = getPreferredTheme();
    setTheme(theme);
    
    // Add event listeners to theme toggle buttons
    themeTogglers.forEach(toggler => {
        toggler.addEventListener('click', () => {
            const theme = toggler.getAttribute('data-bs-theme-value');
            setTheme(theme);
        });
    });
});

// Update theme icon based on current theme
function updateThemeIcon(theme) {
    const themeIcon = document.getElementById('theme-icon');
    if (!themeIcon) return;
    
    // Remove all icon classes
    themeIcon.classList.remove('fa-sun', 'fa-moon', 'fa-circle-half-stroke');
    
    // Add appropriate icon class
    if (theme === 'light') {
        themeIcon.classList.add('fa-sun');
    } else if (theme === 'dark') {
        themeIcon.classList.add('fa-moon');
    } else {
        themeIcon.classList.add('fa-circle-half-stroke');
    }
}

// Organization delete confirmation
function confirmDelete(id) {
    if (confirm('Are you sure you want to delete this organization? This action cannot be undone.')) {
        window.location.href = 'organization.php?delete=' + id;
    }
    return false;
}

// Filter organizations
function filterOrganizations(category) {
    const orgs = document.querySelectorAll('.organization-card');
    orgs.forEach(org => {
        if (category === 'all' || org.getAttribute('data-category') === category) {
            org.style.display = 'block';
        } else {
            org.style.display = 'none';
        }
    });

    // Update active filter button
    const filterButtons = document.querySelectorAll('.filter-btn');
    filterButtons.forEach(btn => {
        if (btn.getAttribute('data-filter') === category) {
            btn.classList.add('active');
        } else {
            btn.classList.remove('active');
        }
    });
}

// Filter functionality
function filterContent(category) {
    console.log('Filtering by category:', category);
    const posts = document.querySelectorAll('.post');
    let postsFound = false;
    
    posts.forEach(post => {
        // Get the post's category (case-insensitive comparison)
        const postCategory = post.getAttribute('data-category').toLowerCase();
        const filterCategory = category.toLowerCase();
        
        // For debugging
        console.log(`Post ID: ${post.id}, Category: ${postCategory}, Filter: ${filterCategory}`);
        
        if (filterCategory === 'all' || postCategory === filterCategory) {
            post.style.display = 'block';
            postsFound = true;
        } else {
            post.style.display = 'none';
        }
    });
    
    // Show a message if no posts match the filter
    const noResultsMessage = document.getElementById('no-filter-results');
    if (!postsFound) {
        if (!noResultsMessage) {
            const feedContent = document.querySelector('.feed-content');
            if (feedContent) {
                const message = document.createElement('div');
                message.id = 'no-filter-results';
                message.className = 'alert alert-info text-center my-4';
                message.innerHTML = `
                    <i class="fas fa-filter fa-2x mb-3"></i>
                    <h5>No posts found</h5>
                    <p>No posts match the selected filter: "${category}"</p>
                    <button class="btn btn-outline-primary btn-sm mt-2" onclick="filterContent('all')">
                        <i class="fas fa-times me-1"></i>Clear Filter
                    </button>
                `;
                feedContent.prepend(message);
            }
        }
    } else if (noResultsMessage) {
        noResultsMessage.remove();
    }

    // Update active filter button
    const allButton = document.querySelector('.filter-btn');
    if (allButton) {
        if (category === 'all') {
            allButton.classList.add('active');
        } else {
            allButton.classList.remove('active');
        }
    }
    
    // Update dropdown items
    const dropdownItems = document.querySelectorAll('.dropdown-item');
    dropdownItems.forEach(item => {
        const itemCategory = item.textContent.trim();
        if (category !== 'all' && itemCategory.toLowerCase().includes(category.toLowerCase())) {
            item.classList.add('active');
        } else {
            item.classList.remove('active');
        }
    });
    
    // Update dropdown button text to show current filter
    const dropdownButton = document.querySelector('.dropdown-toggle');
    if (dropdownButton) {
        if (category === 'all') {
            dropdownButton.innerHTML = '<i class="fas fa-filter me-1"></i> Filter';
        } else {
            // Format the category name for display
            let displayCategory = category;
            if (category === 'financial') {
                displayCategory = 'Financial Reports';
            } else if (category === 'general') {
                displayCategory = 'General';
            } else if (category === 'announcement') {
                displayCategory = 'Announcements';
            } else if (category === 'event') {
                displayCategory = 'Events';
            }
            dropdownButton.innerHTML = `<i class="fas fa-filter me-1"></i> ${displayCategory}`;
        }
    }
}

// Function to handle post reactions
function reactToPost(element, reaction) {
    const reactionContainer = element.closest('.reaction-container');
    const reactionBtn = reactionContainer.querySelector('.reaction-btn');
    const reactionOptions = reactionContainer.querySelector('.reaction-options');
    const currentReaction = reactionBtn.getAttribute('data-current-reaction');
    const newReaction = reaction || 'like';
    const postId = reactionContainer.getAttribute('data-post-id') || '0';
    
    // If same reaction clicked again, remove it
    if (currentReaction === newReaction && reaction) {
        reactionBtn.innerHTML = '<i class="fas fa-thumbs-up"></i> Like';
        reactionBtn.setAttribute('data-current-reaction', 'none');
        reactionBtn.classList.remove(...Array.from(reactionBtn.classList).filter(c => c.startsWith('reaction-')));
        
        // Save to server - remove reaction
        saveReaction(postId, 'none');
        return;
    }
    
    // Update button appearance based on reaction
    let icon, text, className;
    
    switch(newReaction) {
        case 'like':
            icon = 'thumbs-up';
            text = 'Like';
            className = 'reaction-like';
            break;
        case 'love':
            icon = 'heart';
            text = 'Love';
            className = 'reaction-love';
            break;
        case 'care':
            icon = 'hand-holding-heart';
            text = 'Care';
            className = 'reaction-care';
            break;
        case 'haha':
            icon = 'laugh';
            text = 'Haha';
            className = 'reaction-haha';
            break;
        case 'wow':
            icon = 'surprise';
            text = 'Wow';
            className = 'reaction-wow';
            break;
        case 'sad':
            icon = 'sad-tear';
            text = 'Sad';
            className = 'reaction-sad';
            break;
        case 'angry':
            icon = 'angry';
            text = 'Angry';
            className = 'reaction-angry';
            break;
        default:
            icon = 'thumbs-up';
            text = 'Like';
            className = 'reaction-like';
    }
    
    // Remove any existing reaction classes
    reactionBtn.classList.remove(...Array.from(reactionBtn.classList).filter(c => c.startsWith('reaction-')));
    
    // Add new reaction class
    reactionBtn.classList.add(className);
    
    // Update button content
    reactionBtn.innerHTML = `<i class="fas fa-${icon}"></i> ${text}`;
    
    // Update current reaction attribute
    reactionBtn.setAttribute('data-current-reaction', newReaction);
    
    // Save reaction to server
    saveReaction(postId, newReaction);
    
    // Hide reaction options after selection with a delay
    setTimeout(() => {
        reactionOptions.classList.remove('show');
    }, 500);
}

// Function to save reaction to server
function saveReaction(postId, reaction) {
    console.log(`Saving reaction "${reaction}" for post ID: ${postId}`);
    
    // Create form data
    const formData = new FormData();
    formData.append('post_id', postId);
    formData.append('reaction', reaction);
    
    // Send AJAX request to save the reaction
    fetch('save_reaction.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        console.log('Reaction saved:', data);
        if (data.status === 'success') {
            // Update reaction count display
            updateReactionCount(postId);
        } else {
            console.error('Error saving reaction:', data.message);
        }
    })
    .catch(error => {
        console.error('Error saving reaction:', error);
    });
}

// Function to update reaction count display
function updateReactionCount(postId) {
    // Find the reaction count element for this post
    const countElement = document.querySelector(`.post-reactions-count[data-post-id="${postId}"]`);
    if (countElement) {
        // Fetch updated count from server
        fetch(`get_reaction_count.php?post_id=${postId}`)
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    // Update the count display
                    countElement.textContent = data.count > 0 ? data.count : '';
                    
                    // Update reaction icons if provided
                    if (data.reactions) {
                        updateReactionIcons(postId, data.reactions);
                    }
                }
            })
            .catch(error => {
                console.error('Error updating reaction count:', error);
            });
    }
}

// Function to update reaction icons
function updateReactionIcons(postId, reactions) {
    // Implementation depends on how you want to display reaction icons
    console.log(`Updating reaction icons for post ${postId}:`, reactions);
}

// Initialize reaction system
document.addEventListener('DOMContentLoaded', function() {
    const containers = document.querySelectorAll('.reaction-container');
    let hideTimeout;
    
    // Add hover events with delay for each reaction container
    containers.forEach(container => {
        const options = container.querySelector('.reaction-options');
        
        container.addEventListener('mouseenter', () => {
            clearTimeout(hideTimeout);
            options.classList.add('show');
        });
        
        container.addEventListener('mouseleave', () => {
            // Set a timeout before hiding the options
            hideTimeout = setTimeout(() => {
                options.classList.remove('show');
            }, 1200); // 500ms delay before hiding
        });
        
        // Keep options visible when hovering directly over them
        options.addEventListener('mouseenter', () => {
            clearTimeout(hideTimeout);
        });
        
        options.addEventListener('mouseleave', () => {
            hideTimeout = setTimeout(() => {
                options.classList.remove('show');
            }, 500);
        });
    });
    
    // Set up event listeners for reaction options
    document.querySelectorAll('.reaction-option').forEach(option => {
        option.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            const reaction = this.getAttribute('data-reaction');
            reactToPost(this, reaction);
        });
    });
    
    // Set up event listeners for reaction buttons (for direct clicks)
    document.querySelectorAll('.reaction-btn').forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            // If no reaction is selected, use 'like' as default
            if (this.getAttribute('data-current-reaction') === 'none') {
                reactToPost(this, 'like');
            } else {
                // If already has a reaction, toggle it off
                reactToPost(this, this.getAttribute('data-current-reaction'));
            }
        });
    });
});

// Function to handle sharing a post
function sharePost(button) {
    const confirmation = confirm("Do you want to share this post?");
    if (confirmation) {
        alert("Post shared!");
    }
}

// Function to add a comment to a post
function addComment(button) {
    const commentInput = button.previousElementSibling;
    const commentText = commentInput.value.trim();
    if (commentText) {
        const commentsSection = button.parentElement.querySelector('.comments');
        const newComment = document.createElement('div');
        newComment.textContent = commentText;
        commentsSection.appendChild(newComment);
        commentInput.value = ''; // Clear input after posting
    }
}

// Comment functions
function editComment(commentId) {
    // Find the comment element
    const commentItem = document.querySelector(`.comment-item[data-comment-id="${commentId}"]`);
    if (!commentItem) return;
    
    const commentContent = commentItem.querySelector('.comment-content');
    const currentContent = commentContent.textContent.trim();
    
    // Create edit form
    const editForm = document.createElement('div');
    editForm.className = 'edit-comment-form mt-2';
    editForm.innerHTML = `
        <textarea class="form-control mb-2">${currentContent}</textarea>
        <div class="d-flex justify-content-end">
            <button class="btn btn-sm btn-secondary me-2" onclick="cancelEditComment(${commentId})">Cancel</button>
            <button class="btn btn-sm btn-primary" onclick="saveEditComment(${commentId})">Save</button>
        </div>
    `;
    
    // Hide the content and show the form
    commentContent.style.display = 'none';
    commentItem.appendChild(editForm);
}

function cancelEditComment(commentId) {
    // Find the comment element
    const commentItem = document.querySelector(`.comment-item[data-comment-id="${commentId}"]`);
    if (!commentItem) return;
    
    const commentContent = commentItem.querySelector('.comment-content');
    const editForm = commentItem.querySelector('.edit-comment-form');
    
    // Show the content and remove the form
    commentContent.style.display = 'block';
    if (editForm) {
        commentItem.removeChild(editForm);
    }
}

function saveEditComment(commentId) {
    // Find the comment element
    const commentItem = document.querySelector(`.comment-item[data-comment-id="${commentId}"]`);
    if (!commentItem) return;
    
    const editForm = commentItem.querySelector('.edit-comment-form');
    const textarea = editForm.querySelector('textarea');
    const newContent = textarea.value.trim();
    
    if (newContent === '') {
        alert('Comment cannot be empty');
        return;
    }
    
    // Send AJAX request to update the comment
    const formData = new FormData();
    formData.append('comment_id', commentId);
    formData.append('content', newContent);
    
    fetch('edit_comment.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            // Update the comment content
            const commentContent = commentItem.querySelector('.comment-content');
            commentContent.innerHTML = newContent.replace(/\n/g, '<br>');
            commentContent.style.display = 'block';
            
            // Remove the edit form
            commentItem.removeChild(editForm);
        } else {
            alert(data.message);
        }
    })
    .catch(error => {
        console.error('Error updating comment:', error);
        alert('An error occurred while updating the comment');
    });
}

function deleteComment(commentId) {
    if (!confirm('Are you sure you want to delete this comment? This action cannot be undone.')) {
        return;
    }
    
    // Send AJAX request to delete the comment
    const formData = new FormData();
    formData.append('comment_id', commentId);
    
    fetch('delete_comment.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            // Remove the comment element from the DOM
            const commentItem = document.querySelector(`.comment-item[data-comment-id="${commentId}"]`);
            if (commentItem) {
                commentItem.remove();
            }
        } else {
            alert(data.message);
        }
    })
    .catch(error => {
        console.error('Error deleting comment:', error);
        alert('An error occurred while deleting the comment');
    });
}

// messages
// Example messages data - fix the image paths
const messages = [
    { name: "John Doe", preview: "Hey! How are you?", time: "2:30 PM", image: "img/message-profile/p1.jpg" },
    { name: "James Smith", preview: "Don't forget our meeting.", time: "1:15 PM", image: "img/message-profile/p2.jpg" },
    { name: "Michael Brown", preview: "I'll call you later.", time: "12:00 PM", image: "img/message-profile/p3.jpg" },
    { name: "Mike Davis", preview: "Thanks for the update!", time: "Yesterday", image: "img/message-profile/p4.jpg" }
];

// Function to render messages
function renderMessages() {
    const messagesContainer = document.getElementById("messages");
    
    // Add null check to prevent errors
    if (!messagesContainer) {
        console.log('Messages container not found on this page - skipping message rendering');
        return;
    }
    
    // Clear existing content
    messagesContainer.innerHTML = '';
    
    // Check if messages array exists and has items
    if (!messages || !messages.length) {
        console.warn('No messages to display');
        return;
    }
    
    messages.forEach(message => {
        const messageElement = document.createElement("div");
        messageElement.classList.add("message");
        
        // Fix the image src attribute (remove href=)
        messageElement.innerHTML = `
            <img src="${message.image.replace('href=', '')}" alt="${message.name}" />
            <div class="message-content">
                <h4>${message.name}</h4>
                <p>${message.preview}</p>
            </div>
            <span class="message-time">${message.time}</span>
        `;
        messagesContainer.appendChild(messageElement);
    });
    
    console.log('Messages rendered successfully');
}

// Only render messages if we're on a page that has the messages container
document.addEventListener('DOMContentLoaded', function() {
    const messagesContainer = document.getElementById("messages");
    if (messagesContainer) {
        renderMessages();
    } else {
        console.log('Messages container not found on this page - skipping message rendering');
    }
});

// Notification popup functionality
document.addEventListener('DOMContentLoaded', function() {
    const notificationIcon = document.getElementById('notification-icon');
    const notificationPopup = document.getElementById('notification-popup');
    const closePopup = document.getElementById('close-popup');
    
    // Add console logs to debug
    console.log('Notification elements:', { 
        notificationIcon, 
        notificationPopup, 
        closePopup 
    });
    
    if (notificationIcon && notificationPopup && closePopup) {
        notificationIcon.addEventListener('click', function(event) {
            event.preventDefault(); // Prevent default link behavior
            console.log('Notification icon clicked');
            notificationPopup.style.display = 'block';  // Make the popup visible
            setTimeout(function() {
                notificationPopup.classList.add('show');  // Add the show class for animation
                notificationPopup.style.right = '0';  // Slide it in
            }, 10);
        });
        
        closePopup.addEventListener('click', function() {
            notificationPopup.classList.remove('show');  // Remove the show class
            notificationPopup.style.right = '-300px';  // Slide it out
            setTimeout(function() {
                notificationPopup.style.display = 'none';  // Hide it after sliding out
            }, 300); // Wait for the animation to complete before hiding
        });
        
        // Close popup when clicking outside
        document.addEventListener('click', function(event) {
            if (notificationPopup.style.display === 'block' && 
                !notificationPopup.contains(event.target) && 
                event.target !== notificationIcon &&
                !notificationIcon.contains(event.target)) {
                notificationPopup.classList.remove('show');
                notificationPopup.style.right = '-300px';
                setTimeout(function() {
                    notificationPopup.style.display = 'none';
                }, 300);
            }
        });
    } else {
        console.error('Notification elements not found in the DOM');
    }
    
    // Check if messages container exists before rendering
    const messagesContainer = document.getElementById("messages");
    if (messagesContainer) {
        renderMessages();
    } else {
        console.warn('Messages container not found, skipping renderMessages()');
    }
});

// Update theme icon based on current theme
function updateThemeIcon(theme) {
  const themeIcon = document.getElementById('theme-icon');
  if (!themeIcon) return;
  
  // Remove all existing classes
  themeIcon.classList.remove('fa-sun', 'fa-moon', 'fa-circle-half-stroke');
  
  // Add appropriate icon class based on theme
  if (theme === 'dark') {
    themeIcon.classList.add('fa-moon');
  } else if (theme === 'light') {
    themeIcon.classList.add('fa-sun');
  } else {
    themeIcon.classList.add('fa-circle-half-stroke');
  }
}

// Dark mode switcher - improved version
(() => {
  'use strict'

  const getStoredTheme = () => localStorage.getItem('theme')
  const setStoredTheme = theme => localStorage.setItem('theme', theme)

  const getPreferredTheme = () => {
    const storedTheme = getStoredTheme()
    if (storedTheme) {
      return storedTheme
    }

    return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
  }

  const setTheme = theme => {
    if (theme === 'auto' && window.matchMedia('(prefers-color-scheme: dark)').matches) {
      document.documentElement.setAttribute('data-bs-theme', 'dark')
    } else {
      document.documentElement.setAttribute('data-bs-theme', theme)
    }
    
    // Update the theme icon
    updateThemeIcon(theme);
    
    // Also set a cookie for server-side rendering
    document.cookie = `theme=${theme}; path=/; max-age=31536000`; // 1 year
  }

  // Apply theme immediately when the script runs
  setTheme(getPreferredTheme())

  const showActiveTheme = (theme, focus = false) => {
    const themeSwitcher = document.querySelector('#bd-theme')

    if (!themeSwitcher) {
      return
    }

    const btnToActive = document.querySelector(`[data-bs-theme-value="${theme}"]`)
    if (!btnToActive) {
      return
    }

    document.querySelectorAll('[data-bs-theme-value]').forEach(element => {
      element.classList.remove('active')
      element.setAttribute('aria-pressed', 'false')
    })

    btnToActive.classList.add('active')
    btnToActive.setAttribute('aria-pressed', 'true')

    if (focus) {
      themeSwitcher.focus()
    }
  }

  window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', () => {
    const storedTheme = getStoredTheme()
    if (storedTheme !== 'light' && storedTheme !== 'dark') {
      setTheme(getPreferredTheme())
    }
  })

  window.addEventListener('DOMContentLoaded', () => {
    // Apply theme immediately when DOM is loaded
    const theme = getPreferredTheme();
    setTheme(theme)
    showActiveTheme(theme)
    updateThemeIcon(theme)

    document.querySelectorAll('[data-bs-theme-value]')
      .forEach(toggle => {
        toggle.addEventListener('click', () => {
          const theme = toggle.getAttribute('data-bs-theme-value')
          setStoredTheme(theme)
          setTheme(theme)
          showActiveTheme(theme, true)
          
          // Update charts if they exist
          if (typeof updateChartsForTheme === 'function') {
            updateChartsForTheme(theme)
          }
        })
      })
  })
})()

// Add this function to test dark mode
function checkDarkMode() {
  const currentTheme = document.documentElement.getAttribute('data-bs-theme');
  console.log('Current theme:', currentTheme);
  return currentTheme === 'dark';
}

// Organization-specific dark mode adjustments
document.addEventListener('DOMContentLoaded', function() {
    // Apply theme immediately when DOM is loaded
    const currentTheme = getPreferredTheme();
    setTheme(currentTheme);
    
    // Update organization charts if they exist
    if (typeof updateChartsForTheme === 'function') {
        updateChartsForTheme(currentTheme);
    }
    
    // Add event listeners to theme toggles
    document.querySelectorAll('[data-bs-theme-value]').forEach(toggle => {
        toggle.addEventListener('click', () => {
            const theme = toggle.getAttribute('data-bs-theme-value');
            setStoredTheme(theme);
            setTheme(theme);
            
            // Update charts when theme changes
            if (typeof updateChartsForTheme === 'function') {
                updateChartsForTheme(theme);
            }
        });
    });
});

// Function to update charts based on theme (if using charts in organization dashboard)
function updateChartsForTheme(theme) {
    // Check if Chart object exists
    if (typeof Chart === 'undefined') return;
    
    // Get all charts
    const charts = Chart.instances;
    if (!charts || charts.length === 0) return;
    
    // Update each chart
    for (let chart of Object.values(charts)) {
        if (theme === 'dark') {
            // Dark theme chart settings
            chart.options.scales.x.grid.color = 'rgba(255, 255, 255, 0.1)';
            chart.options.scales.y.grid.color = 'rgba(255, 255, 255, 0.1)';
            chart.options.scales.x.ticks.color = '#EEEEEE';
            chart.options.scales.y.ticks.color = '#EEEEEE';
        } else {
            // Light theme chart settings
            chart.options.scales.x.grid.color = 'rgba(0, 0, 0, 0.1)';
            chart.options.scales.y.grid.color = 'rgba(0, 0, 0, 0.1)';
            chart.options.scales.x.ticks.color = '#666666';
            chart.options.scales.y.ticks.color = '#666666';
        }
        chart.update();
    }
}

// Create Post Modal Functionality
document.addEventListener('DOMContentLoaded', function() {
    // Preview uploaded photos in the create post modal
    const photoInput = document.getElementById('post-photos');
    if (photoInput) {
        photoInput.addEventListener('change', function(event) {
            const preview = document.getElementById('photo-preview');
            preview.innerHTML = '';
            
            if (this.files) {
                const maxPhotos = 25;
                const fileCount = Math.min(this.files.length, maxPhotos);
                const previewLimit = 12; // Show only first 12 photos in preview
                
                if (this.files.length > maxPhotos) {
                    alert(`You can only upload a maximum of ${maxPhotos} photos. Only the first ${maxPhotos} will be used.`);
                }
                
                // Create a container for the previews
                const previewContainer = document.createElement('div');
                previewContainer.className = 'row g-2 mt-2';
                preview.appendChild(previewContainer);
                
                // Show preview count if many files
                if (fileCount > 0) {
                    const countInfo = document.createElement('div');
                    countInfo.className = 'mb-2 text-muted';
                    countInfo.textContent = `${fileCount} photo${fileCount > 1 ? 's' : ''} selected`;
                    preview.insertBefore(countInfo, previewContainer);
                }
                
                // Show limited preview
                const showCount = Math.min(fileCount, previewLimit);
                for (let i = 0; i < showCount; i++) {
                    const reader = new FileReader();
                    
                    reader.onload = function(e) {
                        const col = document.createElement('div');
                        col.className = 'col-4 col-md-2';
                        
                        const imgContainer = document.createElement('div');
                        imgContainer.className = 'position-relative';
                        
                        const img = document.createElement('img');
                        img.src = e.target.result;
                        img.className = 'img-fluid rounded';
                        img.style.height = '80px';
                        img.style.objectFit = 'cover';
                        
                        imgContainer.appendChild(img);
                        col.appendChild(imgContainer);
                        previewContainer.appendChild(col);
                        
                        // Add +X overlay on the last preview if there are more photos
                        if (i === showCount - 1 && fileCount > previewLimit) {
                            const overlay = document.createElement('div');
                            overlay.className = 'position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center';
                            overlay.style.backgroundColor = 'rgba(0,0,0,0.5)';
                            overlay.style.borderRadius = '0.25rem';
                            
                            const overlayText = document.createElement('span');
                            overlayText.className = 'text-white fw-bold';
                            overlayText.textContent = `+${fileCount - previewLimit}`;
                            
                            overlay.appendChild(overlayText);
                            imgContainer.appendChild(overlay);
                        }
                    }
                    
                    reader.readAsDataURL(this.files[i]);
                }
            }
        });
    }
    
    // Facebook preview functionality
    window.previewFacebookPage = function() {
        const fbLink = document.getElementById('fb-link').value;
        if (fbLink) {
            const previewContainer = document.getElementById('fb-preview-container');
            
            // Clear previous content
            previewContainer.innerHTML = '';
            
            // Create loading indicator
            const loadingDiv = document.createElement('div');
            loadingDiv.className = 'text-center p-4';
            loadingDiv.innerHTML = '<i class="fas fa-spinner fa-spin fa-2x mb-3"></i><p>Loading preview...</p>';
            previewContainer.appendChild(loadingDiv);
            
            // Show the container
            previewContainer.style.display = 'block';
            
            // Create iframe after a short delay to show loading state
            setTimeout(() => {
                // Remove loading indicator
                previewContainer.innerHTML = '';
                
                // Create and append iframe
                const iframe = document.createElement('iframe');
                iframe.src = `https://www.facebook.com/plugins/page.php?href=${encodeURIComponent(fbLink)}&tabs=timeline&width=500&height=300&small_header=true&adapt_container_width=true&hide_cover=false&show_facepile=true`;
                iframe.width = "100%";
                iframe.height = "300";
                iframe.style.border = "none";
                iframe.style.overflow = "hidden";
                iframe.scrolling = "no";
                iframe.frameBorder = "0";
                iframe.allowFullscreen = true;
                iframe.allow = "autoplay; clipboard-write; encrypted-media; picture-in-picture; web-share";
                
                previewContainer.appendChild(iframe);
            }, 500);
        } else {
            alert('Please enter a Facebook page URL first');
        }
    };
    
    // Handle visibility and organization selection
    const visibilityField = document.getElementById('visibility');
    const organizationField = document.getElementById('organization_id');
    
    if (visibilityField && organizationField) {
        visibilityField.addEventListener('change', function() {
            if (this.value === 'organization' && !organizationField.value) {
                alert('Organization-only visibility requires selecting an organization');
                this.value = 'all';
            }
        });
        
        organizationField.addEventListener('change', function() {
            if (!this.value && visibilityField.value === 'organization') {
                alert('You must select an organization for organization-only visibility');
                visibilityField.value = 'all';
            }
        });
    }
    
    // Handle form submission
    const submitBtn = document.getElementById('submitPostBtn');
    if (submitBtn) {
        submitBtn.addEventListener('click', function() {
            const form = document.getElementById('createPostForm');
            if (!form) {
                console.error('Form not found');
                return;
            }
            
            // Ensure the form has the correct action URL
            if (!form.action || form.action === '') {
                // Check if we're in the organizations directory
                if (window.location.pathname.includes('/organizations/')) {
                    form.action = '../process_post.php';
                } else {
                    form.action = 'process_post.php';
                }
            }
            
            const formData = new FormData(form);
            const statusDiv = document.getElementById('post-status');
            
            // Show loading state
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Creating...';
            if (statusDiv) {
                statusDiv.innerHTML = '<div class="text-info"><i class="fas fa-info-circle me-2"></i>Creating your post...</div>';
            }
            
            // Validate form
            if (!form.checkValidity()) {
                form.reportValidity();
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="fas fa-save me-2"></i>Create Post';
                if (statusDiv) {
                    statusDiv.innerHTML = '<div class="text-danger"><i class="fas fa-exclamation-circle me-2"></i>Please fill all required fields</div>';
                }
                return;
            }
            
            // Add debugging
            console.log('Submitting form data:');
            for (let pair of formData.entries()) {
                console.log(pair[0] + ': ' + pair[1]);
            }
            
            // Submit form via AJAX
            fetch(form.action, {
                method: 'POST',
                body: formData
            })
            .then(response => {
                console.log('Response status:', response.status);
                if (!response.ok) {
                    throw new Error('Server returned ' + response.status);
                }
                return response.text();
            })
            .then(text => {
                // Use safe JSON parsing
                const data = safeJSONParse(text);
                
                // If parsing returned an error object, handle it
                if (data && data.status === 'error') {
                    throw new Error(data.message || 'Unknown error occurred');
                }
                
                return data;
            })
            .then(data => {
                if (!data) {
                    throw new Error('Failed to parse response data');
                }
                
                console.log('Processed response data:', data);
                
                if (data.status === 'success') {
                    // Show success message
                    if (statusDiv) {
                        statusDiv.innerHTML = '<div class="text-success"><i class="fas fa-check-circle me-2"></i>' + data.message + '</div>';
                    }
                    
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = '<i class="fas fa-save me-2"></i>Create Post';
                    
                    // Close the modal after a short delay
                    setTimeout(() => {
                        // Find the modal instance and close it
                        const createPostModal = document.getElementById('createPostModal');
                        if (createPostModal) {
                            const modal = bootstrap.Modal.getInstance(createPostModal);
                            if (modal) {
                                modal.hide();
                                
                                // Refresh the page to show the new post
                                window.location.reload();
                            }
                        }
                    }, 1000); // 1 second delay to show the success message
                } else {
                    // Show error message
                    if (statusDiv) {
                        statusDiv.innerHTML = '<div class="text-danger"><i class="fas fa-exclamation-circle me-2"></i>' + data.message + '</div>';
                    }
                    submitBtn.disabled = false;
                    submitBtn.innerHTML = '<i class="fas fa-save me-2"></i>Create Post';
                }
            })
            .catch(error => {
                console.error('Error:', error);
                if (statusDiv) {
                    let errorMessage = error.message || 'An unknown error occurred';
                    
                    // If we have a full error from safeJSONParse, show a more detailed message
                    if (error.fullError) {
                        console.error('Full server error:', error.fullError);
                        errorMessage += '<br><small>Server details: ' + 
                            (error.fullError.substring(0, 100) + '...').replace(/</g, '&lt;').replace(/>/g, '&gt;') + 
                            '</small>';
                    }
                    
                    statusDiv.innerHTML = '<div class="text-danger"><i class="fas fa-exclamation-circle me-2"></i>' + errorMessage + '</div>';
                }
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="fas fa-save me-2"></i>Create Post';
            });
        });
    }
    
    // Reset form when modal is closed
    const createPostModal = document.getElementById('createPostModal');
    if (createPostModal) {
        createPostModal.addEventListener('hidden.bs.modal', function() {
            const form = document.getElementById('createPostForm');
            const statusDiv = document.getElementById('post-status');
            const submitBtn = document.getElementById('submitPostBtn');
            
            // Reset form and UI elements
            form.reset();
            document.getElementById('photo-preview').innerHTML = '';
            document.getElementById('fb-preview-container').innerHTML = '';
            statusDiv.innerHTML = '';
            submitBtn.disabled = false;
            submitBtn.innerHTML = '<i class="fas fa-save me-2"></i>Create Post';
        });
    }
});

// Function to safely parse JSON with error handling
function safeJSONParse(text) {
    try {
        // Log the raw response for debugging
        console.log('Raw response to parse:', text);
        
        // Check if the response is empty or null
        if (!text || text.trim() === '') {
            console.error('Empty response received');
            return { status: 'error', message: 'Empty response received from server' };
        }
        
        // Check if the response contains PHP errors or warnings
        if (text && typeof text === 'string') {
            if (text.includes('Warning') || text.includes('Fatal error') || text.includes('Notice')) {
                console.error('PHP error detected in response:', text);
                // Extract error message for better display
                const errorMatch = text.match(/<b>(Warning|Fatal error|Notice)<\/b>:\s*(.*?)<br/);
                const errorMessage = errorMatch ? errorMatch[2] : 'PHP error occurred';
                return { 
                    status: 'error', 
                    message: 'Server error: ' + errorMessage,
                    fullError: text
                };
            }
            
            if (text.includes('<br>') || text.includes('<b>') || text.includes('<?php')) {
                // Find the JSON part of the response (usually after the HTML)
                const jsonStart = text.indexOf('{');
                const jsonEnd = text.lastIndexOf('}');
                
                if (jsonStart !== -1 && jsonEnd !== -1 && jsonEnd > jsonStart) {
                    const jsonPart = text.substring(jsonStart, jsonEnd + 1);
                    console.log('Extracted JSON part:', jsonPart);
                    return JSON.parse(jsonPart);
                } else {
                    console.error('No valid JSON found in response:', text);
                    return { 
                        status: 'error', 
                        message: 'Invalid response format from server',
                        fullError: text
                    };
                }
            }
        }
        
        // Regular JSON response
        return JSON.parse(text);
    } catch (e) {
        console.error('Parse error:', e);
        console.error('Failed to parse text:', text);
        return { 
            status: 'error', 
            message: 'Failed to parse server response: ' + e.message,
            fullError: text
        };
    }
}

// Fix for modal close error
document.addEventListener('DOMContentLoaded', function() {
    // Add event listener for all modals to prevent errors when closing
    const modals = document.querySelectorAll('.modal');
    modals.forEach(modal => {
        modal.addEventListener('hidden.bs.modal', function(event) {
            // Safely handle form reset
            const formId = this.querySelector('form')?.id;
            if (formId) {
                const form = document.getElementById(formId);
                if (form) {
                    form.reset();
                    
                    // Handle specific forms
                    if (formId === 'createPostForm') {
                        // Reset photo preview
                        const photoPreview = document.getElementById('photo-preview');
                        if (photoPreview) {
                            photoPreview.innerHTML = '';
                        }
                        
                        // Reset FB preview
                        const fbPreview = document.getElementById('fb-preview-container');
                        if (fbPreview) {
                            fbPreview.innerHTML = '';
                        }
                        
                        // Reset modal title
                        const modalTitle = document.getElementById('createPostModalLabel');
                        if (modalTitle) {
                            modalTitle.innerHTML = '<i class="fas fa-plus-circle me-2"></i>Create Post';
                        }
                        
                        // Reset submit button
                        const submitBtn = document.getElementById('submitPostBtn');
                        if (submitBtn) {
                            submitBtn.innerHTML = '<i class="fas fa-save me-2"></i>Create Post';
                        }
                        
                        // Remove post ID if it exists
                        const postIdInput = document.getElementById('edit_post_id');
                        if (postIdInput) {
                            postIdInput.remove();
                        }
                    }
                }
            }
        });
    });
});

// Edit post form submission
document.getElementById('editPostForm')?.addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const statusDiv = document.getElementById('editPostStatus');
    
    // Show loading state
    if (statusDiv) {
        statusDiv.innerHTML = '<div class="text-info"><i class="fas fa-spinner fa-spin me-2"></i>Updating post...</div>';
    }
    
    fetch(this.action, {
        method: 'POST',
        body: formData
    })
    .then(response => response.text())
    .then(text => {
        console.log('Raw response:', text);
        
        try {
            // Extract JSON from response that might contain PHP warnings
            let jsonData;
            if (text.includes('<br>') || text.includes('<b>')) {
                const jsonStart = text.indexOf('{');
                if (jsonStart !== -1) {
                    const jsonPart = text.substring(jsonStart);
                    jsonData = JSON.parse(jsonPart);
                } else {
                    throw new Error('No JSON found in response');
                }
            } else {
                jsonData = JSON.parse(text);
            }
            
            if (jsonData.status === 'success') {
                if (statusDiv) {
                    statusDiv.innerHTML = '<div class="text-success"><i class="fas fa-check-circle me-2"></i>' + jsonData.message + '</div>';
                }
                
                // Close modal after 1 second
                setTimeout(() => {
                    const modal = bootstrap.Modal.getInstance(document.getElementById('editPostModal'));
                    if (modal) {
                        modal.hide();
                        // Reload the page to show updated content
                        window.location.reload();
                    }
                }, 1000);
            } else {
                if (statusDiv) {
                    statusDiv.innerHTML = '<div class="text-danger"><i class="fas fa-exclamation-circle me-2"></i>' + jsonData.message + '</div>';
                }
            }
        } catch (error) {
            console.error('Error parsing response:', error);
            if (statusDiv) {
                statusDiv.innerHTML = '<div class="text-danger"><i class="fas fa-exclamation-circle me-2"></i>An error occurred. Please try again.</div>';
            }
        }
    })
    .catch(error => {
        console.error('Fetch error:', error);
        if (statusDiv) {
            statusDiv.innerHTML = '<div class="text-danger"><i class="fas fa-exclamation-circle me-2"></i>Network error. Please try again.</div>';
        }
    });
});
