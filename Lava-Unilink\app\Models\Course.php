<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

class Course extends Model
{
    use HasFactory;

    protected $fillable = [
        'code',
        'name',
        'description',
        'department',
        'credits',
        'semester',
        'instructor',
        'capacity',
    ];

    /**
     * Get all users enrolled in this course.
     */
    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'user_courses')
                    ->withPivot(['status', 'grade'])
                    ->withTimestamps();
    }

    /**
     * Get enrolled students only.
     */
    public function enrolledStudents(): BelongsToMany
    {
        return $this->users()->wherePivot('status', 'enrolled');
    }

    /**
     * Check if the course is at capacity.
     */
    public function isAtCapacity(): bool
    {
        if (!$this->capacity) {
            return false;
        }

        return $this->enrolledStudents()->count() >= $this->capacity;
    }

    /**
     * Get available spots in the course.
     */
    public function getAvailableSpotsAttribute(): int
    {
        if (!$this->capacity) {
            return PHP_INT_MAX;
        }

        return max(0, $this->capacity - $this->enrolledStudents()->count());
    }
}
